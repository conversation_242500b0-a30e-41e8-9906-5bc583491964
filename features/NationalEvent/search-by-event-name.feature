@UI-Regression
Feature: Search By Event Name In Promotion Management
    @UI-18748
    Scenario: Verify search functionality by Event Name for National User
        Given "National Merchant" logs into MEUPP application
        And User clicks the view dropdown
        And User selects "<viewDropdownOption>" from the view dropdown
        And User clicks 'Sort & Filter' icon
        And User expands the 'Event Type' Filter
        And User selects "<eventType>" from Event type in Sort & Filter
        And User clicks the 'Apply' button in the 'Sort & Filter' section
        And User clicks the close icon of the 'Sort & Filter' section
        And User Gathers EventNames List From Promotion Management Table
        And User clicks the default-selected 'Offer ID#' search criteria option dropdown
        And User selects "<searchCriteriaOption>" from the search criteria option dropdown
        And User enters the event ID or eventName "<searchCriteriaOption>" in the search criteria input field
        And User clicks the 'Search' icon
        And User clicks the National Event's record displayed as result of search in the search result section
        Examples:
            | viewDropdownOption | searchCriteriaOption | eventType |
            | Planning           | Event Name           | National  |
