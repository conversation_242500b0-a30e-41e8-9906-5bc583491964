{"name": "meupp-e2e-automation", "version": "1.0.0", "description": "Unified Promotions Platform (MEUPP) E2E automation repo for UI automation using Playwright", "main": "index.js", "scripts": {"cucumber-test": "npx @cucumber/cucumber src/ui/features --require-module ts-node/register --require src/ui/step-definitions/**/*.ts --require config/**/*.ts --format json:cucumber-report/cucumber-report.json && node config/htmlReport.js", "cucumber-test:tag": "npx @cucumber/cucumber src/ui/features --require-module ts-node/register --require src/ui/step-definitions/**/*.ts --require config/**/*.ts --tags", "run-feature": "npx @cucumber/cucumber", "start": "npx playwright test --ui --config=config/playwright.config.ts", "test": "npx playwright test --config=config/playwright.config.ts", "test:debug": "npx playwright test --config=config/playwright.config.ts --headed --debug", "test-headed": "npx playwright test --config=config/playwright.config.ts --headed", "show-report": "npx playwright show-report", "test:all": "npx playwright test --config=config/playwright.config.ts --headed --trace on-first-retry", "test:setup": "npx playwright test --config=config/playwright.config.ts", "start-codegen": "npx playwright codegen", "playWright": "npx playwright install", "test:dev": "TEST_ENV=dev playwright test --config=config/playwright.config.ts --headed", "test:qa1": "TEST_ENV=qa1 playwright test --config=config/playwright.config.ts --headed", "test:qa2": "TEST_ENV=qa2 playwright test --config=config/playwright.config.ts --headed", "test:perf1": "TEST_ENV=perf1 playwright test --config=config/playwright.config.ts --headed", "test:stage": "TEST_ENV=stage playwright test --config=config/playwright.config.ts --headed", "generate-html-report": "node config/htmlReport.js"}, "repository": {"type": "git", "url": "https://github.albertsons.com/albertsons/MEUPP-E2E-AUTOMATION.git"}, "dependencies": {"@google-cloud/local-auth": "^3.0.1", "@playwright/mcp": "^0.0.26", "chai": "^4.3.6", "cucumber-html-reporter": "^5.5.0", "date-fns": "^4.1.0", "googleapis": "^149.0.0", "mongodb": "^6.16.0", "playwright": "^1.53.1", "prettier": "^2.6.2"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@cucumber/cucumber": "^11.3.0", "@playwright/test": "^1.53.1", "@types/node": "^22.15.32", "cross-env": "^7.0.3", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}