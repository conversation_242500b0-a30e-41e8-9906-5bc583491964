# Deployment environments Release Managers
> - merchandising-release-managers
# Unified Promotions Platform (MEUPP) E2E Automation

This repository contains end-to-end (E2E) automation tests for the Unified Promotions Platform using Playwright.

## Quick Start

1. **Install Dependencies**
```bash
npm install
```

2. **Install Playwright Browsers**
```bash
npm run playWright
```

## Running Tests

Tests can be run in different environments (DEV, QA1, QA2, PERF1, STAGE). Each command will:
- Set the correct environment configuration
- Handle authentication automatically
- Run tests in headed mode (visible browser)

```bash
npm run test:dev     # Run tests in DEV environment
npm run test:qa1     # Run tests in QA1 environment
npm run test:qa2     # Run tests in QA2 environment
npm run test:perf1   # Run tests in PERF1 environment
npm run test:stage   # Run tests in STAGE environment
```

### Additional Test Commands

```bash
npm run test:debug   # Run tests with debugging enabled
npm test            # Run tests in headless mode
npm run show-report # View test results report
```

## Project Structure

```
├── PageObjectModel/       # Page Object Models
│   ├── EventCreation/    # Event creation related pages
│   └── OfferCreation/    # Offer creation related pages
├── src/
│   ├── Components/       # Reusable UI components
│   └── DynamicConfiguration/ # Environment configurations
├── tests/                # Test specifications
└── utils/               # Utility functions
```

## Environment Configuration

Environment settings are managed in `config.json`. Each environment has:
- Base URL
- Authentication credentials
- Domain configuration
- APIM paths

## Environment Variables

The following environment variables are used:

- `TEST_ENV`: Determines which environment configuration to use
  ```bash
  # Available values:
  - dev    (Development environment)
  - qa1    (QA1 environment)
  - qa2    (QA2 environment)
  - perf1  (Performance testing environment)
  - stage  (Staging environment)
  ```

These are automatically set when using the npm run commands:
```bash
npm run test:dev     # Sets TEST_ENV=dev
npm run test:qa1     # Sets TEST_ENV=qa1
# etc...
```

## Authentication

Authentication is handled automatically through `global-setup.ts`, which:
1. Loads environment-specific configuration
2. Performs login if needed
3. Stores authentication state for future test runs

## Test Reports

After running tests, you can view the HTML report:
```bash
npm run show-report
```

## Debug Mode

For debugging tests:
```bash
npm run test:debug
```
This will:
- Open browser in headed mode
- Enable Playwright's debug mode
- Allow step-by-step execution

## Additional Features

- **Codegen**: Generate tests using Playwright's codegen
  ```bash
  npm run start-codegen
  ```
- **UI Mode**: Run tests with Playwright's UI mode
  ```bash
  npm run start
  ```
- **Cucumber Tests**: Run Cucumber feature tests
  ```bash
  npm run cucumber-test
  ```

## Best Practices

1. Use Page Object Model pattern for maintainable tests
2. Keep tests independent and atomic
3. Use environment-specific configuration from config.json
4. Leverage existing components from src/Components
5. Follow the existing project structure for new tests

## Troubleshooting

If you encounter authentication issues:
1. Check environment configuration in config.json
2. Verify network access to the environment
3. Check console output for detailed error messages
4. Look for screenshots in test-results directory
