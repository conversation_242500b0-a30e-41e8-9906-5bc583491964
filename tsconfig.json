{"compilerOptions": {"target": "ES2018", "module": "commonjs", "moduleResolution": "node", "esModuleInterop": true, "resolveJsonModule": true, "allowJs": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "outDir": "./dist", "rootDir": ".", "typeRoots": ["./node_modules/@types"], "types": ["@playwright/test", "node"]}, "include": ["**/*.ts", "**/*.js", "src/ui/**/*.ts"], "exclude": ["node_modules"]}