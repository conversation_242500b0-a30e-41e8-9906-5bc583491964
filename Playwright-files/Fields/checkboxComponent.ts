import { Page } from '@playwright/test';

export async function clickCheckbox(label: string) {
    const checkbox = globalThis.page.getByLabel(label);
    await checkbox.click();
}

export async function isCheckboxChecked(label: string): Promise<boolean> {
    const checkbox = globalThis.page.getByLabel(label);
    return await checkbox.isChecked();
}

export async function setCheckboxState(label: string, checked: boolean) {
    const checkbox = globalThis.page.getByLabel(label);
    const isCurrentlyChecked = await checkbox.isChecked();
    
    if (isCurrentlyChecked !== checked) {
        await checkbox.click();
    }
}