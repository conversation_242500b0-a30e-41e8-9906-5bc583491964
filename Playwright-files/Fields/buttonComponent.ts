import { Page } from "playwright";

export async function clickButton(page: Page, name: string) {
    await page.getByRole('button', { name }).click();
}

export async function isButtonVisible(page: Page, name: string): Promise<boolean> {
    const button = page.getByRole('button', { name });
    return await button.isVisible();
}

export async function waitForButtonAndClick(page: Page, name: string) {
    const button = page.getByRole('button', { name });
    await button.waitFor({ state: 'visible' });
    await button.click();
}