import { test, expect } from '@playwright/test';

test('Allowance Flow Item Flat', async ({ page }) => {
  await page.goto('https://memsp-dev.albertsons.com/memsp-ui-shell/meupp/');
  await page.getByRole('link', { name: 'Add Event' }).click();
  await page.getByRole('button', { name: 'Get Started' }).nth(2).click();
  await page.getByRole('textbox', { name: 'Search Promo Product Groups' }).click();
  await page.getByRole('textbox', { name: 'Search Promo Product Groups' }).fill('826898');
  await page.getByText('826898 - EVOLVED CHOCOLATE').click();
  await page.locator('form > div').click();
  await page.getByRole('button', { name: 'Vehicle Type/Custom Date' }).click();
  await page.getByText('Promo Cycle').click();
  await page.getByRole('button', { name: 'Start Week/Vehicle' }).click();
  await page.getByText('4wk Feature Wk 36 2025 - 09/').click();
  await page.getByRole('button', { name: 'Save Event Details & Add' }).click();
  await page.getByRole('button', { name: 'Remove Invalid Divisions' }).click();
  await page.getByRole('button', { name: 'Save Event Details & Add' }).click();
  await page.locator('button[name="allowanceType"]').click();
  const itemFlatSpan = page.locator('#abs-input-select-container > div > div > div > div > div:nth-child(3) > div > div > span');
  await expect(itemFlatSpan).toBeVisible();
  await itemFlatSpan.click();
  await page.getByRole('button', { name: 'Enter Amounts' }).click();
  await page.getByRole('spinbutton').click();
  await page.getByRole('spinbutton').fill('0.2');
  await page.getByRole('button', { name: 'Update All Divisions' }).click();
  await page.getByRole('button', { name: 'Save All Changes' }).click();
  await page.getByRole('button', { name: 'Save & Create Allowance(s)' }).click();
  await page.getByRole('button', { name: '+ Add Another Offer &' }).isVisible();
  await expect(page.getByText('Item Flat')).toBeVisible();
  await expect(page.getByText('0.2')).toBeVisible();
});



