Test #1: 

Description: Case DSD Offer creation by selecting "One Allowance: DSD Combined" radio button in the first stepper of the allowance workflow

1. <PERSON><PERSON> as a National Merchant
2. Click 'Add Event'
3. Click 'Get Started' option that is present besides the 'National'
4. Verify the new event edit panel
5. Select a DSD PPG from the 'Promo Product Groups' dropdown
6. Validate 'Store Group Type' value and stores count from 'View stores' under the 'Store Groups' field
7. Select 'Promo Cycle' from the 'Vehicle Type/Custom Date*' field's dropdown
8. Select a start week/ vehicle from the 'Start Week/Vehicle' field's dropdown
9. Click 'Save Event Details & Add Allowance' button
10. Click 'Remove Invalid Divisions' link
11. Click 'Save Event Details & Add Allowance' button
12. Select 'Case' from 'Allowance Type*' drop down
13. Verify the display of 'One Allowance: DSD Combined' and 'Separate Allowances By DSD Distributor' radio buttons beneath the 'Allowance Type*' and 'Performance*' fields' dropdowns
14. Select 'One Allowance: DSD Combined' radio button
15. Verify the display of 'Allowance Dates' text beneath the 'Allowance Type*' and 'Performance*' fields' dropdowns
16. Click 'Enter Amonts' button
17. Enter allowance amount to 'Allow amounts:' field's textbox
18. Click 'Update All Divisions' button
19. Click 'Save All Changes' button
20. Click 'Save & Create Allowance(s)' button
21. Expand the created offer
22. Expand the firstly displayed allowance
23. Verify whether 'One Allowance: DSD Combined' is displayed besides the 'Allowance to be Created' field