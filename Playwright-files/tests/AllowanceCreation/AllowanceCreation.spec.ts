import {
  regenerateAuthState,
  clearAuthState,
  isAuthStateValid,
} from "../../../src/Utils/auth-utils";
import { getConfig } from "../../../src/Utils/config-utils";
import { test } from "../../../PageObjectModel/Fixtures/PromtionMangementFixtures";
import { nationalEventCreationData } from "../../DynamicConfiguration/Event/National/createEventConfig";  
import { NationalEventsOfferCreationSectionPage } from "../../../PageObjectModel/OfferCreation/NationalEventsOfferCreationSectionPage";

const env = process.env.TEST_ENV || "dev";
const { baseUrl } = getConfig(env);

// Configure test timeouts and retries
test.setTimeout(120000); // Global timeout of 120s
test.use({ actionTimeout: 60000 }); // Action timeout of 60s

test.beforeEach(async ({ page, context }) => {
  console.log('Starting beforeEach hook...');
  await page.goto(baseUrl, { waitUntil: 'domcontentloaded' });

  try {
    // Wait for navigation and initial load with increased timeout
    await Promise.race([
      page.waitForLoadState("networkidle", { timeout: 90000 }),
      page.waitForSelector('.abs-pm-promotion-head__heading', { timeout: 90000 })
    ]);
    console.log('Initial page load complete');

    // Handle permission errors and validate auth state
    const hasPermissionError = await page.locator("text=You do not have permission").isVisible();
    if (hasPermissionError) {
      console.log("Permission error detected. Regenerating auth.json...");
      await clearAuthState();
      await regenerateAuthState(page, context);
      await page.goto(baseUrl);
      await page.waitForLoadState("networkidle");
    }

    if (!(await isAuthStateValid({ page }))) {
      console.log("auth.json is invalid. Regenerating...");
      await regenerateAuthState(page, context);
      await page.goto(baseUrl);
      await page.waitForLoadState("networkidle");
    }
  } catch (error) {
    console.error('Error in beforeEach hook:', error);
    throw error;
  }
});

const vehicleOptions: ('Promo Cycle' | 'Custom Date' | 'Other')[] = [
  'Promo Cycle',
  'Custom Date',
  'Other',
];

test('Case DSD Offer creation by selecting "One Allowance: DSD Combined" radio button in the first stepper of the allowance workflow', async ({ page, nationalEventCreationPage, newEventTypeSelect, nationalEventsOfferCreationSection }) => {

  // Create a National Event with DSD PPG
  await newEventTypeSelect.selectEventType(baseUrl, 'NDP');
  await nationalEventCreationPage.populateProductGroupNational(nationalEventCreationData?.dsdPpg);
  await nationalEventCreationPage.populateStoreGroupsData(nationalEventCreationData?.storeGroup);
  await nationalEventCreationPage.populateVehicleTypeOrCustomDateNational(vehicleOptions[0]);
  await nationalEventCreationPage.saveEventDetails();

  // Create and submit Case DSD offer by selecting 'One Allowance: DSD Combined' radio button in the first stepper of the allowance workflow
  await nationalEventsOfferCreationSection.verifyNewOfferHeader(page);
  await nationalEventsOfferCreationSection.clickAllowanceTypeDropdown();
  await nationalEventsOfferCreationSection.selectAllowanceType('Case');
  await nationalEventsOfferCreationSection.ensureDefaultSelectionAtOrSelectStepperOneRadioButtonOptionForCaseOffer('DSD', 'One Allowance: DSD Combined');
  await nationalEventsOfferCreationSection.clickEnterAmountsButton();
  await nationalEventsOfferCreationSection.enterAllowanceAmountOnMainEntryScreen('1');
  await nationalEventsOfferCreationSection.clickUpdateAllDivisionsButton();
  await nationalEventsOfferCreationSection.clickSaveAllChangesButton();
  await nationalEventsOfferCreationSection.waitForEventEditPage();
  await nationalEventsOfferCreationSection.clickSaveAndCreateAllowancesButton();
  await nationalEventsOfferCreationSection.allowanceToBeCreatedFieldsValueValidation('DSD','One Allowance: DSD Combined');
});

test('Case DSD Offer creation by selecting "Separate Allowances By DSD Distributor" radio button in the first stepper of the allowance workflow', async ({ page, nationalEventCreationPage, newEventTypeSelect, nationalEventsOfferCreationSection }) => {
  
  // Create a National Event with DSD PPG
  await newEventTypeSelect.selectEventType(baseUrl, 'NDP');
  await nationalEventCreationPage.populateProductGroupNational(nationalEventCreationData?.dsdPpg);
  await nationalEventCreationPage.populateStoreGroupsData(nationalEventCreationData?.storeGroup);
  await nationalEventCreationPage.populateVehicleTypeOrCustomDateNational(vehicleOptions[0]);
  await nationalEventCreationPage.saveEventDetails();

  // Create and submit Case DSD offer by selecting 'Separate Allowances By DSD Distributor' radio button in the first stepper of the allowance workflow
  await nationalEventsOfferCreationSection.verifyNewOfferHeader(page);
  await nationalEventsOfferCreationSection.clickAllowanceTypeDropdown();
  await nationalEventsOfferCreationSection.selectAllowanceType('Case');
  await nationalEventsOfferCreationSection.ensureDefaultSelectionAtOrSelectStepperOneRadioButtonOptionForCaseOffer('DSD', 'Separate Allowances By DSD Distributor');
  await nationalEventsOfferCreationSection.clickEnterAmountsButton();
  await nationalEventsOfferCreationSection.enterAllowanceAmountOnMainEntryScreen('1');
  await nationalEventsOfferCreationSection.clickUpdateAllDivisionsButton();
  await nationalEventsOfferCreationSection.clickSaveAllChangesButton();
  await nationalEventsOfferCreationSection.waitForEventEditPage();
  await nationalEventsOfferCreationSection.clickSaveAndCreateAllowancesButton();
  await nationalEventsOfferCreationSection.allowanceToBeCreatedFieldsValueValidation('DSD','Separate Allowances By DSD Distributor');
});

test('Case Warehouse Offer creation and verification of "Allowance to be Created" field value within an allowance in the offer', async ({ page, nationalEventCreationPage, newEventTypeSelect, nationalEventsOfferCreationSection }) => {
  
  // Create a National Event with Warehouse PPG
  await newEventTypeSelect.selectEventType(baseUrl, 'NDP');
  await nationalEventCreationPage.populateProductGroupNational(nationalEventCreationData?.warehousePpg);
  await nationalEventCreationPage.populateStoreGroupsData(nationalEventCreationData?.storeGroup);
  await nationalEventCreationPage.populateVehicleTypeOrCustomDateNational(vehicleOptions[0]);
  await nationalEventCreationPage.saveEventDetails();

  await nationalEventsOfferCreationSection.verifyNewOfferHeader(page);
  await nationalEventsOfferCreationSection.clickAllowanceTypeDropdown();
  await nationalEventsOfferCreationSection.selectAllowanceType('Case');
  await nationalEventsOfferCreationSection.ensureDefaultSelectionAtOrSelectStepperOneRadioButtonOptionForCaseOffer('Warehouse', 'Warehouse Only');
  await nationalEventsOfferCreationSection.clickEnterAmountsButton();
  await nationalEventsOfferCreationSection.clickOkButtonOnOneTimePopupMessage(true);
  await nationalEventsOfferCreationSection.enterAllowanceAmountOnMainEntryScreen('1');
  await nationalEventsOfferCreationSection.clickUpdateAllDivisionsButton();
  await nationalEventsOfferCreationSection.clickSaveAllChangesButton();
  await nationalEventsOfferCreationSection.waitForEventEditPage();
  await nationalEventsOfferCreationSection.clickSaveAndCreateAllowancesButton();
  await nationalEventsOfferCreationSection.allowanceToBeCreatedFieldsValueValidation('Warehouse','Warehouse Only');
});

test('Case Warehouse DSD Offer creation and verification of "Allowance to be Created" field value within an allowance in the offer to be "Both"', async ({ page, nationalEventCreationPage, newEventTypeSelect, nationalEventsOfferCreationSection }) => {
  
  // Create a National Event with Warehouse PPG
  await newEventTypeSelect.selectEventType(baseUrl, 'NDP');
  await nationalEventCreationPage.populateProductGroupNational(nationalEventCreationData?.warehousePpg);
  await nationalEventCreationPage.populateProductGroupNational(nationalEventCreationData?.dsdPpg);
  await nationalEventCreationPage.populateStoreGroupsData(nationalEventCreationData?.storeGroup);
  await nationalEventCreationPage.populateVehicleTypeOrCustomDateNational(vehicleOptions[0]);
  await nationalEventCreationPage.saveEventDetails();

  await nationalEventsOfferCreationSection.verifyNewOfferHeader(page);
  await nationalEventsOfferCreationSection.clickAllowanceTypeDropdown();
  await nationalEventsOfferCreationSection.selectAllowanceType('Case');
  await nationalEventsOfferCreationSection.ensureDefaultSelectionAtOrSelectStepperOneRadioButtonOptionForCaseOffer('Warehouse DSD', 'Both');
  await nationalEventsOfferCreationSection.clickEnterAmountsButton();
  await nationalEventsOfferCreationSection.enterAllowanceAmountOnMainEntryScreen('1');
  await nationalEventsOfferCreationSection.clickUpdateAllDivisionsButton();
  await nationalEventsOfferCreationSection.clickSaveAllChangesButton();
  await nationalEventsOfferCreationSection.waitForEventEditPage();
  await nationalEventsOfferCreationSection.clickContinueToWarehouseDatesButton();
  await nationalEventsOfferCreationSection.clickReviewOverlapsAndEnterAmountsButton();
  await nationalEventsOfferCreationSection.enterAllowanceAmountOnMainEntryScreen('1');
  await nationalEventsOfferCreationSection.clickUpdateAllDivisionsButton();
  await nationalEventsOfferCreationSection.clickSaveAllChangesButton();
  await nationalEventsOfferCreationSection.waitForEventEditPage();
  await nationalEventsOfferCreationSection.clickSaveAndCreateAllowancesButton();
  await nationalEventsOfferCreationSection.allowanceToBeCreatedFieldsValueValidation('Warehouse DSD','Both');
});

test('Case Warehouse DSD Offer creation and verification of "Allowance to be Created" field value within an allowance in the offer to be "Warehouse Only"', async ({ page, nationalEventCreationPage, newEventTypeSelect, nationalEventsOfferCreationSection }) => {

  // Create a National Event with Warehouse PPG
  await newEventTypeSelect.selectEventType(baseUrl, 'NDP');
  await nationalEventCreationPage.populateProductGroupNational(nationalEventCreationData?.warehousePpg);
  await nationalEventCreationPage.populateProductGroupNational(nationalEventCreationData?.dsdPpg);
  await nationalEventCreationPage.populateStoreGroupsData(nationalEventCreationData?.storeGroup);
  await nationalEventCreationPage.populateVehicleTypeOrCustomDateNational(vehicleOptions[0]);
  await nationalEventCreationPage.saveEventDetails();

  await nationalEventsOfferCreationSection.verifyNewOfferHeader(page);
  await nationalEventsOfferCreationSection.clickAllowanceTypeDropdown();
  await nationalEventsOfferCreationSection.selectAllowanceType('Case');
  await nationalEventsOfferCreationSection.ensureDefaultSelectionAtOrSelectStepperOneRadioButtonOptionForCaseOffer('Warehouse DSD', 'Warehouse Only');
  await nationalEventsOfferCreationSection.clickEnterAmountsButton();
  await nationalEventsOfferCreationSection.clickOkButtonOnOneTimePopupMessage(true);
  await nationalEventsOfferCreationSection.enterAllowanceAmountOnMainEntryScreen('1');
  await nationalEventsOfferCreationSection.clickUpdateAllDivisionsButton();
  await nationalEventsOfferCreationSection.clickSaveAllChangesButton();
  await nationalEventsOfferCreationSection.waitForEventEditPage();
  await nationalEventsOfferCreationSection.clickSaveAndCreateAllowancesButton();
  await nationalEventsOfferCreationSection.allowanceToBeCreatedFieldsValueValidation('Warehouse DSD','Warehouse Only');
});

test('Case Warehouse DSD Offer creation and verification of "Allowance to be Created" field value within an allowance in the offer to be "DSD Only"', async ({ page, nationalEventCreationPage, newEventTypeSelect, nationalEventsOfferCreationSection }) => {
  
  // Create a National Event with Warehouse PPG
  await newEventTypeSelect.selectEventType(baseUrl, 'NDP');
  await nationalEventCreationPage.populateProductGroupNational(nationalEventCreationData?.warehousePpg);
  await nationalEventCreationPage.populateProductGroupNational(nationalEventCreationData?.dsdPpg);
  await nationalEventCreationPage.populateStoreGroupsData(nationalEventCreationData?.storeGroup);
  await nationalEventCreationPage.populateVehicleTypeOrCustomDateNational(vehicleOptions[0]);
  await nationalEventCreationPage.saveEventDetails();

  await nationalEventsOfferCreationSection.verifyNewOfferHeader(page);
  await nationalEventsOfferCreationSection.clickAllowanceTypeDropdown();
  await nationalEventsOfferCreationSection.selectAllowanceType('Case');
  await nationalEventsOfferCreationSection.ensureDefaultSelectionAtOrSelectStepperOneRadioButtonOptionForCaseOffer('Warehouse DSD', 'DSD Only');
  await nationalEventsOfferCreationSection.clickEnterAmountsButton();
  await nationalEventsOfferCreationSection.enterAllowanceAmountOnMainEntryScreen('1');
  await nationalEventsOfferCreationSection.clickUpdateAllDivisionsButton();
  await nationalEventsOfferCreationSection.clickSaveAllChangesButton();
  await nationalEventsOfferCreationSection.waitForEventEditPage();
  await nationalEventsOfferCreationSection.clickSaveAndCreateAllowancesButton();
  await nationalEventsOfferCreationSection.allowanceToBeCreatedFieldsValueValidation('Warehouse DSD','DSD Only');
});

test('Header Flat Offer creation', async ({ page, nationalEventCreationPage, newEventTypeSelect, nationalEventsOfferCreationSection }) => {
  
  // Create a National Event with DSD PPG
  await newEventTypeSelect.selectEventType(baseUrl, 'NDP');
  await nationalEventCreationPage.populateProductGroupNational(nationalEventCreationData?.dsdPpg);
  await nationalEventCreationPage.populateStoreGroupsData(nationalEventCreationData?.storeGroup);
  await nationalEventCreationPage.populateVehicleTypeOrCustomDateNational(vehicleOptions[0]);
  await nationalEventCreationPage.saveEventDetails();

  // Create and submit Header Flat offer
  await nationalEventsOfferCreationSection.verifyNewOfferHeader(page);
  await nationalEventsOfferCreationSection.clickAllowanceTypeDropdown();
  await nationalEventsOfferCreationSection.selectAllowanceType('Header Flat');
  await nationalEventsOfferCreationSection.clickHeaderFlatAllowanceAmountTextBox();
  await nationalEventsOfferCreationSection.enterHeaderFlatAllowanceAmount('0.2');
  await nationalEventsOfferCreationSection.clickContinueToBillingDetailsButton();
  await nationalEventsOfferCreationSection.clickSaveAndCreateAllowancesButton();
  await nationalEventsOfferCreationSection.verifyHeaderFlatOffersAllowanceType();
});

test('Allowance Creation Of ShipToStore With Combined DSD', async ({ page, eventCreationPage, nationalEventCreationPage, eventLandingPage, newEventTypeSelect, nationalEventsOfferCreationSection }) => {
  // await eventCreationPage.createNewEvent(vehicleOptions[0], nationalEventCreationPage, eventLandingPage, newEventTypeSelect);
  await newEventTypeSelect.selectEventType(baseUrl, 'NDP');
  await nationalEventCreationPage.populateProductGroupNational(nationalEventCreationData?.dsdPpg);
  await nationalEventCreationPage.populateStoreGroupsData(nationalEventCreationData?.storeGroup);
  await nationalEventCreationPage.populateVehicleTypeOrCustomDateNational(vehicleOptions[0]);
  await nationalEventCreationPage.saveEventDetails();
  await page.waitForLoadState("networkidle");
  await nationalEventsOfferCreationSection.verifyNewOfferHeader(page);
  await nationalEventsOfferCreationSection.clickAllowanceTypeDropdown();
  await nationalEventsOfferCreationSection.selectAllowanceType('Ship To Store');
  await nationalEventsOfferCreationSection.configureAllowance('Combined DSD', '1');
});

test('Allowance Creation Of ShipToStore With Separate Allowance by DSD distributor', async ({ page, eventCreationPage, nationalEventCreationPage, eventLandingPage, newEventTypeSelect, nationalEventsOfferCreationSection }) => {
  // await eventCreationPage.createNewEvent(vehicleOptions[0], nationalEventCreationPage, eventLandingPage, newEventTypeSelect);
  await newEventTypeSelect.selectEventType(baseUrl, 'NDP');
  await nationalEventCreationPage.populateProductGroupNational(nationalEventCreationData?.dsdPpg);
  await nationalEventCreationPage.populateStoreGroupsData(nationalEventCreationData?.storeGroup);
  await nationalEventCreationPage.populateVehicleTypeOrCustomDateNational(vehicleOptions[0]);
  await nationalEventCreationPage.saveEventDetails();
  await page.waitForLoadState("networkidle");
  await nationalEventsOfferCreationSection.configureAllowance('Separate Allowances By DSD Distributor', '1');
});


test('Verify Offer Deal Sheet with Separate Allowance by  DSD distributor', async ({ page, eventCreationPage, nationalEventCreationPage, eventLandingPage, newEventTypeSelect, nationalEventsOfferCreationSection }) => {
  // await eventCreationPage.createNewEvent(vehicleOptions[0], nationalEventCreationPage, eventLandingPage, newEventTypeSelect);
    await newEventTypeSelect.selectEventType(baseUrl, 'NDP');
  await nationalEventCreationPage.populateProductGroupNational(nationalEventCreationData?.dsdPpg);
  await nationalEventCreationPage.populateStoreGroupsData(nationalEventCreationData?.storeGroup);
  await nationalEventCreationPage.populateVehicleTypeOrCustomDateNational(vehicleOptions[0]);
  await nationalEventCreationPage.saveEventDetails();
  await page.waitForLoadState("networkidle");
  await nationalEventsOfferCreationSection.configureAllowance('Separate Allowances By DSD Distributor', '1');
  await nationalEventsOfferCreationSection.viewOfferDealSheet();
});

test('Verify Offer Deal Sheet For combined DSD', async ({ page, eventCreationPage, nationalEventCreationPage, eventLandingPage, newEventTypeSelect, nationalEventsOfferCreationSection }) => {
  // await eventCreationPage.createNewEvent(vehicleOptions[0], nationalEventCreationPage, eventLandingPage, newEventTypeSelect);
    await newEventTypeSelect.selectEventType(baseUrl, 'NDP');
  await nationalEventCreationPage.populateProductGroupNational(nationalEventCreationData?.dsdPpg);
  await nationalEventCreationPage.populateStoreGroupsData(nationalEventCreationData?.storeGroup);
  await nationalEventCreationPage.populateVehicleTypeOrCustomDateNational(vehicleOptions[0]);
  await nationalEventCreationPage.saveEventDetails();
  await page.waitForLoadState("networkidle");
  await nationalEventsOfferCreationSection.configureAllowance('Combined DSD', '1');
  await nationalEventsOfferCreationSection.viewOfferDealSheet();
});

test('Verify Summary Page For combined DSD', async ({ page, eventCreationPage, nationalEventCreationPage, eventLandingPage, newEventTypeSelect, nationalEventsOfferCreationSection }) => {
  // await eventCreationPage.createNewEvent(vehicleOptions[0], nationalEventCreationPage, eventLandingPage, newEventTypeSelect);
    await newEventTypeSelect.selectEventType(baseUrl, 'NDP');
  await nationalEventCreationPage.populateProductGroupNational(nationalEventCreationData?.dsdPpg);
  await nationalEventCreationPage.populateStoreGroupsData(nationalEventCreationData?.storeGroup);
  await nationalEventCreationPage.populateVehicleTypeOrCustomDateNational(vehicleOptions[0]);
  await nationalEventCreationPage.saveEventDetails();
  await page.waitForLoadState("networkidle");
  await nationalEventsOfferCreationSection.configureAllowance('Combined DSD', '1');
  await nationalEventsOfferCreationSection.clickViewSummaryIcon();
  await nationalEventsOfferCreationSection.verifyAllowanceAmountIsVisible();

});

test('Verify summary page for separate allowance by DSD distributor', async ({ page, eventCreationPage, nationalEventCreationPage, eventLandingPage, newEventTypeSelect, nationalEventsOfferCreationSection }) => {
  // await eventCreationPage.createNewEvent(vehicleOptions[0], nationalEventCreationPage, eventLandingPage, newEventTypeSelect);
  await newEventTypeSelect.selectEventType(baseUrl, 'NDP');
  await nationalEventCreationPage.populateProductGroupNational(nationalEventCreationData?.dsdPpg);
  await nationalEventCreationPage.populateStoreGroupsData(nationalEventCreationData?.storeGroup);
  await nationalEventCreationPage.populateVehicleTypeOrCustomDateNational(vehicleOptions[0]);
  await nationalEventCreationPage.saveEventDetails();
  await page.waitForLoadState("networkidle");
  await nationalEventsOfferCreationSection.configureAllowance('Separate Allowances By DSD Distributor', '1');
  await nationalEventsOfferCreationSection.clickViewSummaryIcon();
 await nationalEventsOfferCreationSection.verifyAllowanceAmountIsVisible();
});

test('Allowance Creation Of Scan With One Allowance Warehouse', async ({ page, eventCreationPage, nationalEventCreationPage, eventLandingPage, newEventTypeSelect, nationalEventsOfferCreationSection }) => {
  //  await eventCreationPage.createNewEvent(vehicleOptions[0], nationalEventCreationPage, eventLandingPage, newEventTypeSelect);
   await newEventTypeSelect.selectEventType(baseUrl, 'NDP');
  await nationalEventCreationPage.populateProductGroupNational(nationalEventCreationData?.dsdPpg);
  await nationalEventCreationPage.populateStoreGroupsData(nationalEventCreationData?.storeGroup);
  await nationalEventCreationPage.populateVehicleTypeOrCustomDateNational(vehicleOptions[0]);
  await nationalEventCreationPage.saveEventDetails();
  await page.waitForLoadState("networkidle");
   await nationalEventsOfferCreationSection.createScanAllowance('one');
 });

 test('Allowance Creation Of Scan With Separate Allowance By DSD distributor', async ({ page, eventCreationPage, nationalEventCreationPage, eventLandingPage, newEventTypeSelect, nationalEventsOfferCreationSection }) => {
     await newEventTypeSelect.selectEventType(baseUrl, 'NDP');
     await nationalEventCreationPage.populateProductGroupNational(nationalEventCreationData?.dsdPpg);
    await nationalEventCreationPage.populateStoreGroupsData(nationalEventCreationData?.storeGroup);
    await nationalEventCreationPage.populateVehicleTypeOrCustomDateNational(vehicleOptions[0]);
   await nationalEventCreationPage.saveEventDetails();
    await page.waitForLoadState("networkidle");
    await nationalEventsOfferCreationSection.createScanAllowance('separate');
  });