import {
  regenerateAuthState,
  clearAuthState,
  isAuthStateValid,
} from "../../../src/Utils/auth-utils";
import { getConfig } from "../../../src/Utils/config-utils";
import { test } from '@playwright/test';
import { nationalEventCreationData } from "../../DynamicConfiguration/Event/National/createEventConfig";
import { NationalEventCreationPage } from "../../../PageObjectModel/EventCreation/National/NationalEventCreationPage";
import { NewEventTypeSelect } from "../../../src/Components/baseComponent/NewEventTypeSelect/newEventTypeSelect";
import { clickButtonByName, fillInputByRole } from "../../../src/Utils/form-utils";
import { getGmmailVerificationCode, getVerificationCode } from "../../../src/Components/VendorLoginComponent/GmailVerificationCode";
import { authorize } from "../../../src/Components/VendorLoginComponent/VerificationToken";
import { NationalEventsOfferCreationSectionPage } from "../../../PageObjectModel/OfferCreation/NationalEventsOfferCreationSectionPage";

const env = process.env.TEST_ENV || "dev";
const { baseUrl } = getConfig(env);

// Configure test timeouts and retries
test.setTimeout(120000); // Global timeout of 120s
test.use({ actionTimeout: 60000 }); // Action timeout of 60s

test.beforeEach(async ({ page, context }) => {
  console.log('Starting beforeEach hook...');
  await page.goto(baseUrl, { waitUntil: 'domcontentloaded' });

  try {
    // Wait for navigation and initial load with increased timeout
    await Promise.race([
      page.waitForLoadState("networkidle", { timeout: 90000 }),
      page.waitForSelector('.abs-pm-promotion-head__heading', { timeout: 90000 })
    ]);
    console.log('Initial page load complete');

    // Handle permission errors and validate auth state
    const hasPermissionError = await page.locator("text=You do not have permission").isVisible();
    if (hasPermissionError) {
      console.log("Permission error detected. Regenerating auth.json...");
      await clearAuthState();
      await regenerateAuthState(page, context);
      await page.goto(baseUrl);
      await page.waitForLoadState("networkidle");
    }

    if (!(await isAuthStateValid({ page }))) {
      console.log("auth.json is invalid. Regenerating...");
      await regenerateAuthState(page, context);
      await page.goto(baseUrl);
      await page.waitForLoadState("networkidle");
    }
  } catch (error) {
    console.error('Error in beforeEach hook:', error);
    throw error;
  }
});

const vehicleOptions: ('Promo Cycle' | 'Custom Date' | 'Other')[] = [
  'Promo Cycle',
  'Custom Date',
  'Other',
];

test('Case DSD Offer creation by selecting "One Allowance: DSD Combined" radio button in the first stepper of the allowance workflow', async ({ page, context, browser }) => {
  
  // Create an instance of NationalEventCreationPage
    const nationalEventCreationPage = new NationalEventCreationPage(page);
  
    // Create an instance of newEventTypeSelect (import or define as needed)
    const newEventTypeSelect = new NewEventTypeSelect(page);

  // Create a National Event with DSD PPG
  await newEventTypeSelect.selectEventType(baseUrl, 'NDP');
  await nationalEventCreationPage.populateProductGroupNational(nationalEventCreationData?.dsdPpg);
  await nationalEventCreationPage.populateStoreGroupsData(nationalEventCreationData?.storeGroup);
  await nationalEventCreationPage.populateVehicleTypeOrCustomDateNational(vehicleOptions[0]);
  await nationalEventCreationPage.saveEventDetails();

  // Instantiate nationalEventsOfferCreationPage with the available page object
  const nationalEventsOfferCreationSectionPage = new NationalEventsOfferCreationSectionPage(page);

  // Create and submit Case DSD offer by selecting 'One Allowance: DSD Combined' radio button in the first stepper of the allowance workflow
  await nationalEventsOfferCreationSectionPage.verifyNewOfferHeader(page);
  await nationalEventsOfferCreationSectionPage.clickAllowanceTypeDropdown();
  await nationalEventsOfferCreationSectionPage.selectAllowanceType('Case');
  await nationalEventsOfferCreationSectionPage.ensureDefaultSelectionAtOrSelectStepperOneRadioButtonOptionForCaseOffer('DSD', 'One Allowance: DSD Combined');
  await nationalEventsOfferCreationSectionPage.clickEnterAmountsButton();
  await nationalEventsOfferCreationSectionPage.enterAllowanceAmountOnMainEntryScreen('1');
  await nationalEventsOfferCreationSectionPage.clickUpdateAllDivisionsButton();
  await nationalEventsOfferCreationSectionPage.clickSaveAllChangesButton();
  await nationalEventsOfferCreationSectionPage.waitForEventEditPage();
  await nationalEventsOfferCreationSectionPage.clickSaveAndCreateAllowancesButton();
  await nationalEventsOfferCreationSectionPage.allowanceToBeCreatedFieldsValueValidation('DSD','One Allowance: DSD Combined');

  // Click 'Send to Vendor' under 'Actions'
  await page.getByRole('button', { name: 'Send to Vendor' }).click();

  // Locate the event ID from the header and extract the number
  const eventIDText = await page.locator('//div[@id="event-header"]/div/span/span[2]').innerText();
  const match = eventIDText.match(/\d+/);
  const number = match ? Number(match[0]) : null;
  
  console.log(number);

  // Locate the event status from the header and verify it equals 'pending with vendor'
  const eventStatusFetchedAsNationalMerchant = await page.locator('#event-header').getByText('pending with vendor').innerText();
  eventStatusFetchedAsNationalMerchant.includes('pending with vendor');

  // Manually close the current context
  await context.close();

  // Open a new context and page for the National Vendor login
  const latestContext = await browser.newContext({storageState: undefined});
  const newPage = await latestContext.newPage();

  // Navigate to the application's URL and then enter the National Vendor email ID to email input field
  await newPage.goto(baseUrl);
  const emailInput = await newPage.locator('input[type="email"], input[name="loginfmt"], #i0116').first();
  await emailInput.waitFor({ state: 'visible', timeout: 30000 });
  await emailInput.fill("<EMAIL>");
  await newPage.waitForTimeout(1000);

  // Click 'Next' button
  const nextButton = newPage.getByRole('button', { name: 'Next' });
  if (await nextButton.isVisible()) {
      await nextButton.click();
      await newPage.waitForLoadState('networkidle');
      await newPage.waitForTimeout(2000);
  }

  // Wait for the verification code input field to be visible, receive the verification code from Gmail and fill the field with the same
  const code: any = await authorize();
  const codes = await getVerificationCode(code);
  console.log('Fetched code:', codes);
  console.log('Waiting for verification code...', code);
  await fillInputByRole(newPage, "Enter the code you received", codes);

  // Click 'Sign in' button
  await newPage.locator('input#idSIButton9').click();

  // Select 'Planning' option from the view dropdown
  await newPage.getByRole('button', { name: 'Tasks' }).click();
  await newPage.getByText('Planning').click();

  // Select 'Event ID#' option from the search criteria dropdown
  await newPage.getByRole('button', { name: 'Offer ID#' }).click();
  await newPage.getByText('Event ID#').click();

  // Fill the search input with the event ID and click the search icon
  await newPage.getByRole('textbox', { name: 'Search' }).click();
  await newPage.getByRole('textbox', { name: 'Search' }).fill((number !== null && number !== undefined) ? number.toString() : "");
  await newPage.locator('div:nth-child(4) > .lucide').click();

  // Click on the national icon link
  await newPage.getByRole('link', { name: 'national-icon -' }).click();

  // Locate the event status from the header and verify it equals 'pending with vendor'
  const eventStatusFetchedAsNationaVendor = await newPage.locator('#event-header').getByText('pending with vendor').innerText();
  eventStatusFetchedAsNationaVendor.includes('pending with vendor');

  // Click 'Agreed' under 'Actions'
  await newPage.getByRole('button', { name: 'Agreed' }).click();

  // Locate the event status from the header and verify it equals 'pending with vendor'
  const updatedEventStatusFetchedAsNationaVendor = await newPage.locator('#event-header').getByText('pending with vendor').innerText();
  updatedEventStatusFetchedAsNationaVendor.includes('Agreed');

  // Close the context used for the National Vendor login
  await latestContext.close();
});