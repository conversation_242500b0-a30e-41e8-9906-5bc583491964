import {
  regenerateAuthState,
  clearAuthState,
  isAuthStateValid,
} from "../../../src/Utils/auth-utils";
import { getConfig } from "../../../src/Utils/config-utils";
import { EventLandingPage } from "../../Pages/LandingFlow/EventLandingPage";
import { EventCreationPage } from "../../Pages/EventsFlow/EventCreationPage";
import { OfferPage } from "../../Pages/OffersFlow/OfferPage";
import { chromium, test } from "../../../PageObjectModel/Fixtures/PromtionMangementFixtures";
import { getGmmailVerificationCode, getVerificationCode } from "../../../src/Components/VendorLoginComponent/GmailVerificationCode";
import { clickButtonByName, fillInputByRole } from "../../../src/Utils/form-utils";
import { authorize } from "../../../src/Components/VendorLoginComponent/VerificationToken";

const env = process.env.TEST_ENV || "dev";
const { baseUrl } = getConfig(env);

// Configure test timeouts and retries
test.setTimeout(120000); // Global timeout of 120s
test.use({ actionTimeout: 60000 }); // Action timeout of 60s

test.beforeEach(async ({ page, context }) => {
  console.log('Starting beforeEach hook...');
  await page.goto(baseUrl, { waitUntil: 'domcontentloaded' });

  try {
    // Wait for navigation and initial load with increased timeout
    await Promise.race([
      page.waitForLoadState("networkidle", { timeout: 90000 }),
      page.waitForSelector('.abs-pm-promotion-head__heading', { timeout: 90000 })
    ]);
    console.log('Initial page load complete');

    // Handle permission errors and validate auth state
    const hasPermissionError = await page.locator("text=You do not have permission").isVisible();
    if (hasPermissionError) {
      console.log("Permission error detected. Regenerating auth.json...");
      await clearAuthState();
      await regenerateAuthState(page, context);
      await page.goto(baseUrl);
      await page.waitForLoadState("networkidle");
    }

    if (!(await isAuthStateValid({ page }))) {
      console.log("auth.json is invalid. Regenerating...");
      await regenerateAuthState(page, context);
      await page.goto(baseUrl);
      await page.waitForLoadState("networkidle");
    }
  } catch (error) {
    console.error('Error in beforeEach hook:', error);
    throw error;
  }
});


test("Verify landing page and go to Add Event", async ({ page, eventLandingPage }) => {
  await page.waitForLoadState("networkidle");
  await eventLandingPage.verifyLandingPage('Promotion Management', 'Add Event');
  await eventLandingPage.goToAddEventPage('Add Event', '**/events', 'New Event');
});

const vehicleOptions: ('Promo Cycle' | 'Custom Date' | 'Other')[] = [
  'Promo Cycle',
  'Custom Date',
  'Other',
];

test("Create DP (Division Promotion) event", async ({ page, eventCreationPage, divisionPromotionCreationPage, offerPage, eventLandingPage, newEventTypeSelect }) => {
  await eventCreationPage.createNewDivisionPromotionEvent(divisionPromotionCreationPage, eventLandingPage, newEventTypeSelect); // Create event with Vehicle > Promo Cycle
  await page.waitForLoadState("networkidle");

  // await offerPage.createAndSubmitOffer(); // WIP
});

test("Create DAO (Division Allowance Only) event", async ({ eventCreationPage, divisionAllowanceOnlyCreationPage, eventLandingPage, offerPage, newEventTypeSelect }) => {
  await eventCreationPage.createNewDivisionAllowanceOnlyEvent(divisionAllowanceOnlyCreationPage, eventLandingPage, newEventTypeSelect); // Create event with Vehicle > Promo Cycle

  // const offerPage = new OfferPage(page); // WIP
  // await offerPage.createAndSubmitOffer();
});

test("Create event with PID", async ({ eventCreationPage, eventCreationWithPID, eventLandingPage, offerPage, newEventTypeSelect }) => {
  await eventCreationPage.createNationalEventWithPID(eventCreationWithPID, eventLandingPage, newEventTypeSelect); // Create event with Vehicle > Promo Cycle
});

test("Create a new National event with promocycle and submit an offer", async ({ page, eventCreationPage, nationalEventCreationPage, eventLandingPage, offerNationalsPage, newEventTypeSelect }) => {
  // await eventPage.createNewEvent();
  await eventCreationPage.createNewEvent(vehicleOptions[0], nationalEventCreationPage, eventLandingPage, newEventTypeSelect);  // Create event with Vehicle > Promo Cycle

  // const offerPage = new OfferPage(page); // WIP
  // await page.waitForLoadState("networkidle");
  // await offerNationalsPage.createAndSubmitNationalOffer(); // WIP
});


// // WIP
// test("Create event with Custom Date", async ({ eventCreationPage, nationalEventCreationPage, eventLandingPage, offerPage, newEventTypeSelect }) => {
//   await eventCreationPage.createNewEvent(vehicleOptions[1], nationalEventCreationPage, eventLandingPage, newEventTypeSelect); // Create event with Custom Date
// });

// test("Create event with Other", async () => {
//   const eventPage = new EventCreationPage(page);
//   await eventPage.createNewEvent(vehicleOptions[2]); // Create event with Other
// });