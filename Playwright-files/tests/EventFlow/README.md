# Event Flow Test Suite Documentation

## Overview
The Event Flow test suite (`Event.spec.ts`) contains end-to-end tests for the event creation and management functionality in the Promotion Management system. It covers different types of events including Division Promotion (DP), Division Allowance Only (DAO), and National Events.

## Test Configuration
- **Global Timeout**: 120 seconds
- **Action Timeout**: 60 seconds
- **Environment**: Configurable via `TEST_ENV` (defaults to "dev")

## Prerequisites
- Valid authentication credentials (merchant or vendor)
- Required permissions for event creation
- Gmail API setup for vendor verification codes

## Test Cases

### 1. Division Promotion (DP) Event Creation
test("Create DP (Division Promotion) event")

**Purpose**: Creates a Division Promotion event with specific configurations and validates the creation workflow.

**Steps**:
1. Event Type Selection
   - Selects 'DP' (Division Promotion) event type
   - Validates event type selection UI

2. Division Configuration
   - Populates division selection (e.g., "27-SEATTLE")
   - Validates division data loading

3. Product Group Setup
   - Populates Product Group for Division Promotion
   - Sets up PPG (Promo Product Groups)
   - Validates PPG store visibility

4. Store Group Configuration
   - Configures store group type
   - Selects specific store groups
   - Validates store group assignments

5. Vehicle Configuration
   - Sets vehicle type to 'Promo Cycle'
   - Selects year for promotion
   - Configures start week/vehicle
   - Sets vehicle start and end dates
   - Example dates: Start: 08/06/25, End: 09/02/25

6. Event Details Validation
   - Validates event name field presence
   - Verifies all required fields
   - Saves event details

**Key Components Used**:
- `EventCreationPage`: Main event creation workflow
- `DivisionPromotionCreationPage`: Division-specific configurations
- `EventLandingPage`: Navigation and initial setup
- `NewEventTypeSelect`: Event type selection handling

**Validation Points**:
- Event type selection confirmation
- Division data validation
- Store group configuration verification
- Vehicle date range validation
- Final event creation success verification

**Configuration Data**:
const eventCreationData = {
    division: "27-SEATTLE",
    ppg: [Product Group IDs],
    storeGroupType: "All Stores",
    storeGroups: ["Seattle All Stores", "Haggen All Stores"],
    year: "2025",
    vehicleType: {
        week: "05 4wk Feature Wk 32 2025",
        startDate: "08/06/25",
        endDate: "09/02/25"
    }
}

**Error Handling**:
- Navigation timeout handling (90s timeout)
- Permission validation
- Network state verification
- DOM state validation
- Automatic retry on navigation failures

### 2. Division Allowance Only (DAO) Event Creation
test("Create DAO (Division Allowance Only) event")

**Purpose**: Creates a Division Allowance Only event for managing division-specific allowances.

**Steps**:
1. Event Type Selection
   - Selects 'AO' (Allowance Only) event type
   - Verifies Division Allowance Only header

2. Division Configuration
   - Sets division selection
   - Validates division selection UI

3. Product Group Configuration
   - Configures Product Group for Division Allowance Only
   - Views PPG stores
   - Validates store associations

4. Vehicle Setup
   - Sets vehicle type to 'Promo Cycle'
   - Selects year
   - Configures start week/vehicle (e.g., "05 4wk Feature Wk 32 2025")
   - Sets vehicle date range:
     * Start: "08/06/25"
     * End: "09/02/25"

5. Event Details Finalization
   - Validates event name field
   - Verifies input fields
   - Saves event configuration

**Key Components**:
- `DivisionAllowanceOnlyCreationPage`: Allowance-specific configurations
- `EventCreationPage`: Main workflow management
- `EventLandingPage`: Navigation handling

### 3. National Event Creation with PID
test("Create event with PID")

**Purpose**: Creates an event using a Product ID (PID) with specific product-based configurations.

**Steps**:
1. Event Setup
   - Selects 'NDP' (National) event type
   - Validates national event UI

2. PID Configuration
   - Populates PID
   - Validates PID data loading
   - Configures event name

3. Store Groups Setup
   - Populates store groups data
   - Validates store selections

4. Vehicle Configuration
   - Sets vehicle type/custom date
   - Configures week selection
   - Sets vehicle start/end dates

**Key Components**:
- `EventCreationWithPID`: PID-specific handling
- `NewEventTypeSelect`: Event type selection

### 4. National Event with Promo Cycle
test("Create a new National event with promocycle")

**Purpose**: Creates a National level event with promotion cycle configuration.

**Steps**:
1. Event Configuration
   - Selects 'NDP' event type
   - Validates national event screen

2. Product Setup
   - Configures National Product Group
   - Sets store groups data

3. Vehicle Configuration
   - Sets vehicle type to 'Promo Cycle'
   - Configures date ranges
   - Validates cycle settings

4. Offer Creation (WIP)
   - Placeholder for offer submission
   - Future integration with offer workflow

**Configuration Options**:
const vehicleOptions = [
    'Promo Cycle',
    'Custom Date',
    'Other'
];

### 5. Vendor Login Flow
test("vendor login")

**Purpose**: Tests vendor-specific authentication process.

**Steps**:
1. Authentication
   - Enters vendor email (gmail)
   - Handles verification code process
   - Validates login success

2. Gmail Integration
   - Fetches verification code
   - Handles code entry
   - Validates successful authentication

**Key Components**:
- `GmailVerificationCode`: Verification handling
- `VerificationToken`: Token management

## Common Utilities and Functions

### Event Landing Page
async verifyLandingPage(title: string, linkText: string)
async goToAddEventPage(linkText: string, urlPattern: string, header: string)

### Event Creation
async createNewDivisionPromotionEvent()
async createNewDivisionAllowanceOnlyEvent()
async createNewEvent()
async createNationalEventWithPID()

## Test Configuration

### Environment Variables
- `TEST_ENV`: Sets test environment (dev/qa1/qa2)
- `NAVIGATION_TIMEOUT`: 90000ms
- `ACTION_TIMEOUT`: 60000ms

### Timeouts
test.setTimeout(120000); // Global
test.use({ actionTimeout: 60000 }); // Actions

## Error Handling and Retries

### Navigation Errors
try {
    await Promise.race([
        page.waitForLoadState("networkidle", { timeout: 90000 }),
        page.waitForSelector('.abs-pm-promotion-head__heading', { timeout: 90000 })
    ]);
} catch (error) {
    console.error('Navigation failed:', error);
}

### Authentication Validation
if (!(await isAuthStateValid({ page }))) {
    await regenerateAuthState(page, context);
}

## Best Practices for Test Execution
1. Always run tests in clean state
2. Verify authentication before each test
3. Handle timeouts appropriately
4. Clean up test data after execution
5. Use appropriate wait states
6. Validate critical elements before interaction
7. Handle both merchant and vendor scenarios
