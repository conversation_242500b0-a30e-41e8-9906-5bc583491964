import {
  regenerateAuthState,
  clearAuthState,
  isAuthStateValid,
} from "../../src/Utils/auth-utils";
import fs from 'fs';
import { getGmmailVerificationCode, getVerificationCode } from "../../src/Components/VendorLoginComponent/GmailVerificationCode";
import { clickButtonByName, fillInputByRole } from "../../src/Utils/form-utils";
import { authorize } from "../../src/Components/VendorLoginComponent/VerificationToken";
import { getConfig } from "../../src/Utils/config-utils";
import { nationalEventCreationData } from "../DynamicConfiguration/Event/National/createEventConfig";
import { test } from "../../PageObjectModel/Fixtures/PromtionMangementFixtures";

const env = process.env.TEST_ENV || "dev";
const { baseUrl } = getConfig(env);

// Configure test timeouts and retries
test.setTimeout(120000); // Global timeout of 120s
test.use({ actionTimeout: 60000 }); // Action timeout of 60s

test.beforeEach(async ({ page, context }) => {
  console.log('Starting beforeEach hook...');
  await page.goto(baseUrl, { waitUntil: 'domcontentloaded' });

  try {
    // Wait for navigation and initial load with increased timeout
    await Promise.race([
      page.waitForLoadState("networkidle", { timeout: 90000 }),
      page.waitForSelector('.abs-pm-promotion-head__heading', { timeout: 90000 })
    ]);
    console.log('Initial page load complete');

    // Handle permission errors and validate auth state
    const hasPermissionError = await page.locator("text=You do not have permission").isVisible();
    if (hasPermissionError) {
      console.log("Permission error detected. Regenerating auth.json...");
      await clearAuthState();
      await regenerateAuthState(page, context);
      await page.goto(baseUrl);
      await page.waitForLoadState("networkidle");
    }

    if (!(await isAuthStateValid({ page }))) {
      console.log("auth.json is invalid. Regenerating...");
      await regenerateAuthState(page, context);
      await page.goto(baseUrl);
      await page.waitForLoadState("networkidle");
    }
  } catch (error) {
    console.error('Error in beforeEach hook:', error);
    throw error;
  }
});

const vehicleOptions: ('Promo Cycle' | 'Custom Date' | 'Other')[] = [
  'Promo Cycle',
  'Custom Date',
  'Other',
];

test('National event event creation', async ({ page, nationalEventCreationPage, newEventTypeSelect }) => {
  // Create National Event either as Nationa Merchant or National Vendor using DSD PPG
  await newEventTypeSelect.selectEventType(baseUrl, 'NDP');
  await nationalEventCreationPage.populateProductGroupNational(nationalEventCreationData?.dsdPpg);
  await nationalEventCreationPage.populateStoreGroupsData(nationalEventCreationData?.storeGroup);
  await nationalEventCreationPage.populateVehicleTypeOrCustomDateNational(vehicleOptions[0]);
  await nationalEventCreationPage.saveEventDetails();
});

