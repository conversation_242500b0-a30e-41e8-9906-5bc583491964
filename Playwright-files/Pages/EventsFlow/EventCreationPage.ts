import { Page, expect } from '@playwright/test';
import { getConfig } from '../../../src/ui/Utils/config-utils';
import { eventCreationData, nationalEventCreationData, PIDEventCreationData } from '../../DynamicConfiguration/Event/DivsionOnly/createEventConfig';
import { DivisionAllowanceOnlyCreationPage } from '../../../src/ui/PageObjectModel/EventCreation/DivisionAllowanceOnly/DivisionAllowanceOnlyCreationPage';
import { NewEventTypeSelect } from '../../../src/ui/Components/baseComponent/NewEventTypeSelect/newEventTypeSelect';
import { EventLandingPage } from '../LandingFlow/EventLandingPage';
import { NationalEventCreationPage } from '../../../src/ui/PageObjectModel/EventCreation/National/NationalEventCreationPage';
import { EventCreationWithPID } from '../../../src/ui/PageObjectModel/EventCreation/EventCreationWithPID';
import { DivisionPromotionCreationPage } from '../../../src/ui/PageObjectModel/EventCreation/DivisionPromotion/DivisionPromotionCreationPage';

const env = process.env.TEST_ENV || "dev";
const { baseUrl } = getConfig(env);

export class EventCreationPage {
  constructor(private page: Page) {}
  
  async createNewDivisionPromotionEvent(divisionPromotionCreationPage: DivisionPromotionCreationPage, eventLandingPage: EventLandingPage, newEventTypeSelect: NewEventTypeSelect) {
    // Set longer timeouts for this flow
    await this.page.setDefaultTimeout(90000);
    await this.page.setDefaultNavigationTimeout(90000);

    await newEventTypeSelect.selectEventType(baseUrl, 'DP');
    // Initial navigation
    // await this.page.goto(baseUrl);
    // await Promise.race([
    //   this.page.waitForLoadState("networkidle"),
    //   this.page.waitForSelector('.abs-pm-promotion-head__heading')
    // ]);

    // // Verify landing page and navigate to Add Event
    // await eventLandingPage.verifyLandingPage('Promotion Management', 'Add Event');
    // await eventLandingPage.goToAddEventPage('Add Event', '**/events', 'New Event');
    // await this.page.waitForLoadState("networkidle");

    // // Verify initial elements and click Get Started
    // await expect(this.page.getByText('New Event')).toBeVisible();
    // await expect(this.page.getByText('Division Promotion')).toBeVisible();
    // // // Verify we're on the New Event page with a more specific selector
    // // await expect(this.page.locator('h1, .page-title').getByText('New Event')).toBeVisible();
    // // await expect(this.page.getByText('Division Promotion')).toBeVisible();
    
    // // Click Get Started and ensure proper navigation
    // await Promise.all([
    //     this.page.waitForURL('**/events/create?eventType=DP'),
    //     this.page.getByRole('button', { name: 'Get Started' }).first().click()
    //     // await this.page.getByRole('button', { name: 'Get Started' }).nth(1).click();
    // ]);

    // // Wait for the page to be fully loaded before proceeding
    // await this.page.waitForLoadState('networkidle');
    await this.page.waitForLoadState('domcontentloaded');

    // Continue with division selection
    await divisionPromotionCreationPage.divisionSelection(eventCreationData?.division);
    await divisionPromotionCreationPage.populateProductGroupDivisionPromotion(eventCreationData?.ppg);
    await divisionPromotionCreationPage.ViewPPGStores(eventCreationData?.ViewPPGStores);
    await divisionPromotionCreationPage.getStoreGroupType(eventCreationData?.storeGroupType);
    await divisionPromotionCreationPage.getStoreGroups(eventCreationData?.storeGroups);
    await divisionPromotionCreationPage.vehicleTypeOrCustomDate('Promo Cycle');
    await divisionPromotionCreationPage.yearSelection(eventCreationData?.year);
    await divisionPromotionCreationPage.startWeekOrVehicle(eventCreationData?.vehicleType?.week);
    await divisionPromotionCreationPage.prepopulateVehicleStartDate(eventCreationData?.vehicleType?.startDate);
    await divisionPromotionCreationPage.prepopulateVehicleEndDate(eventCreationData?.vehicleType?.endDate);

    // Final verifications
    await expect(this.page.getByText('Event Name')).toBeVisible();
    await expect(this.page.locator('input[name="name"]')).toBeVisible();
    await divisionPromotionCreationPage.saveEventDetails();
  }

  async createNewDivisionAllowanceOnlyEvent(divisionAllowanceOnlyCreationPage: DivisionAllowanceOnlyCreationPage, eventLandingPage: EventLandingPage, newEventTypeSelect: NewEventTypeSelect) {
    
    // await this.page.goto(baseUrl);
    // await this.page.waitForLoadState("networkidle");

    // await eventLandingPage.verifyLandingPage('Promotion Management', 'Add Event');
    // await eventLandingPage.goToAddEventPage('Add Event', '**/events', 'New Event');
    // await this.page.waitForLoadState("networkidle");


    // await this.page.getByRole('button', { name: 'Get Started' }).nth(1).click();
    // await this.page.waitForURL('**/events/create?eventType=AO');

    // await this.page.waitForLoadState("networkidle");
    
    // await expect(this.page.getByText('New Event')).toBeVisible();
    // await expect(this.page.getByText('Division Allowance Only')).toBeVisible();
    
    await newEventTypeSelect.selectEventType(baseUrl, 'AO');

    // Form filling - component methods handle their own wait states
    await divisionAllowanceOnlyCreationPage.divisionSelection(eventCreationData?.division);
    await divisionAllowanceOnlyCreationPage.populateProductGroupDivisionAllowanceOnly(eventCreationData?.ppg);
    await divisionAllowanceOnlyCreationPage.ViewPPGStores(eventCreationData?.ViewPPGStores);
    await divisionAllowanceOnlyCreationPage.vehicleTypeOrCustomDate('Promo Cycle');
    await divisionAllowanceOnlyCreationPage.yearSelection(eventCreationData?.year);
    await divisionAllowanceOnlyCreationPage.startWeekOrVehicle('05 4wk Feature Wk 34 2025 -');
    await divisionAllowanceOnlyCreationPage.prepopulateVehicleStartDate('08/20/25');
    await divisionAllowanceOnlyCreationPage.prepopulateVehicleEndDate('09/16/25');

    // Final verifications
    await expect(this.page.getByText('Event Name')).toBeVisible();
    await expect(this.page.locator('input[name="name"]')).toBeVisible();
    await divisionAllowanceOnlyCreationPage.saveEventDetails();
  }


 
  async createNewEvent(vehicleTypeOption: 'Other' | 'Promo Cycle' | 'Custom Date', nationalEventCreationPage: NationalEventCreationPage, eventLandingPage: EventLandingPage, newEventTypeSelect: NewEventTypeSelect) {
    // Set longer timeouts for this flow
    await this.page.setDefaultTimeout(90000);
    await this.page.setDefaultNavigationTimeout(90000);

    // // Initial navigation
    // await this.page.goto(baseUrl);
    // await Promise.race([
    //   this.page.waitForLoadState("networkidle"),
    //   this.page.waitForSelector('.abs-pm-promotion-head__heading')
    // ]);

    // // Verify landing page and navigate to Add Event
    // await eventLandingPage.verifyLandingPage('Promotion Management', 'Add Event');
    // await eventLandingPage.goToAddEventPage('Add Event', '**/events', 'New Event');
    // await this.page.waitForLoadState("networkidle");
    // await this.page.waitForLoadState('domcontentloaded');

    // // Click Get Started and ensure proper navigation
    // await Promise.all([
    //     this.page.waitForURL('**/events/create?eventType=NDP'),
    //     this.page.getByRole('button', { name: 'Get Started' }).nth(2).click()
    // ]);

    // // Wait for page load before proceeding
    // await this.page.waitForLoadState('networkidle');
    await newEventTypeSelect.selectEventType(baseUrl, 'NDP');

    await this.page.waitForLoadState('domcontentloaded');

    await nationalEventCreationPage.populateProductGroupNational(nationalEventCreationData?.ppg);
    await nationalEventCreationPage.populateStoreGroupsData(nationalEventCreationData?.storeGroup);
    await nationalEventCreationPage.populateVehicleTypeOrCustomDateNational(vehicleTypeOption);
    await nationalEventCreationPage.saveEventDetails();
  }

  async createNationalEventWithPID(eventCreationWithPID: EventCreationWithPID, eventLandingPage: EventLandingPage, newEventTypeSelect: NewEventTypeSelect) {
    // await this.page.goto(baseUrl);
    // await this.page.waitForLoadState("networkidle");

    // await eventLandingPage.verifyLandingPage('Promotion Management', 'Add Event');
    // await eventLandingPage.goToAddEventPage('Add Event', '**/events', 'New Event');

    // await this.page.getByRole('button', { name: 'Get Started' }).nth(2).click();
    // await this.page.waitForURL('**/events/create?eventType=NDP');

    await newEventTypeSelect.selectEventType(baseUrl, 'NDP');

    await eventCreationWithPID.populatePID(PIDEventCreationData?.pid);
    await this.page.waitForLoadState("networkidle");

    // New modified code
    await this.page.waitForLoadState("networkidle");
    await eventCreationWithPID.populateEventName(PIDEventCreationData?.eventName);
    await eventCreationWithPID.populateStoreGroupsData(PIDEventCreationData?.storeGroup);
    await eventCreationWithPID.populateVehicleTypeOrCustomDate(PIDEventCreationData?.vehicleType?.option);
    await eventCreationWithPID.weekSelection(PIDEventCreationData?.vehicleType?.week);
    await eventCreationWithPID.vehicleStartSelection(PIDEventCreationData?.vehicleType?.startDate);
    await eventCreationWithPID.vehicleEndSelection(PIDEventCreationData?.vehicleType?.endDate);
    await eventCreationWithPID.saveEventDetails();
  }
}
