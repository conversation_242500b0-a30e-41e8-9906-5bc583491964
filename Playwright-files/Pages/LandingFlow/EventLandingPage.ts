import { Page, expect, Locator } from '@playwright/test';

export class EventLandingPage {
  constructor(public page: Page) { }

  async verifyLandingPage(headingText: string, linkText: string) {
    // Wait for the main content to load
    await this.page.waitForLoadState('domcontentloaded');
    await this.page.waitForLoadState('networkidle');

    // Check for and handle any loading indicators or overlays
    const loadingOverlay = this.page.locator('.loading-overlay');
    if (await loadingOverlay.isVisible()) {
      await loadingOverlay.waitFor({ state: 'hidden', timeout: 30000 });
    }

    // First check for the promotion header class
    await this.page.waitForSelector('.abs-pm-promotion-head__heading', { timeout: 30000 });

    // Then verify specific heading text
    await expect(this.page.getByRole('heading', { name: headingText }))
      .toBeVisible({ timeout: 30000 });

    await expect(this.page.getByRole('link', { name: linkText }))
      .toBeVisible({ timeout: 30000 });
  }

  async goToAddEventPage(linkText: string, expectedURL: string, confirmationText: string) {
    await this.page.waitForLoadState('domcontentloaded');
    await this.page.getByRole('link', { name: linkText }).click();

    // Wait for URL change and navigation
    await Promise.all([
      this.page.waitForURL(expectedURL),
      this.page.waitForLoadState('networkidle')
    ]);

    // Ensure the page has fully loaded
    await this.page.waitForLoadState('domcontentloaded');
  }
}
