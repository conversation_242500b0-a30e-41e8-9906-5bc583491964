import { Page } from "@playwright/test";
import { clickButtonByName, fillInputByRole } from "../../../src/ui/Utils/form-utils";


export class LoginPage {
  constructor(private page: Page) { }

  async navigate(baseUrl: string) {
    console.log(`Navigating to login: ${baseUrl}`);
    await this.page.goto(baseUrl);
    await this.page.waitForLoadState('domcontentloaded');
    await this.page.waitForLoadState('networkidle');
  }

  async login(username: string, password: string) {
    // Wait for initial redirects to complete
    await this.page.waitForLoadState('networkidle', { timeout: 60000 });
    await this.page.waitForLoadState('domcontentloaded');

    // Extra wait for all frames to load
    await this.page.waitForTimeout(3000);

    // Handle username step - form-utils will handle frame detection
    await fillInputByRole(this.page, "<EMAIL>", username);
    await clickButtonByName(this.page, "Next");
    await this.page.waitForLoadState('networkidle');

    // Extra wait for password field to be ready
    await this.page.waitForTimeout(2000);

    // Handle password step - form-utils will handle frame detection
    await fillInputByRole(this.page, "Password", password);
    await clickButtonByName(this.page, "Sign in");

    // Handle "Stay signed in?" prompt if it appears
    try {
      await this.page.waitForTimeout(2000); // Wait for prompt to appear
      await clickButtonByName(this.page, "Yes");
    } catch (error) {
      console.log('No stay signed in prompt found, continuing...');
    }

    // Wait for full navigation to complete
    await this.page.waitForLoadState('domcontentloaded', { timeout: 60000 });
    await this.page.waitForLoadState('networkidle', { timeout: 60000 });

    // Wait for main app indicator
    await this.page.waitForSelector('.abs-pm-promotion-head__heading', {
      timeout: 60000,
      state: 'visible'
    });
  }
}