import { Page, expect } from '@playwright/test';

export class OfferPage {
  constructor(private page: Page) { }

  async createAndSubmitOffer() {
    await expect(this.page.getByText('New Offer')).toBeVisible();
    await this.page.locator('button[name="allowanceType"]').click();
    // await this.page.getByText(/^Case$/).nth(2).click();
    // await page.goto('https://memsp-dev.albertsons.com/memsp-ui-shell/meupp/events/');
    // await page.locator('button[name="allowanceType"]').click();
    // await page.getByText('Case').click();
    await this.page.waitForLoadState("networkidle");
    // await this.page.getByRole('button', { name: 'Case' }).click();
    


    await this.page.getByText('Scan').click();

    // Wait for the content to be loaded
    await this.page.waitForLoadState('networkidle');
    
    try {
        const labelElement = await this.page.waitForSelector('[data-testid="allowance-to-be-created-content-input"] label', 
            { state: 'visible', timeout: 10000 }
        );
        
        if (await labelElement.isVisible()) {
            const labelText = await labelElement.innerText();
            console.log("labelText>", labelText);

            if (labelText.includes('One Allowance: DSD Combined')) {
                await this.page.getByText('One Allowance: DSD Combined').click();
            } else if (labelText.includes('Warehouse')) {
                const radio = this.page.getByTestId('radio-check');
                const isChecked = await radio.isChecked();
                if (!isChecked) {
                    await radio.click();
                }
                await expect(this.page.getByText('Warehouse Allowance Dates')).toBeVisible();
            } else {
                // Handle the Performance dropdown scenario
                await expect(this.page.getByText('Performance*')).toBeVisible();

                await this.page.getByRole('button', { name: 'Select' }).click();
                await this.page.getByRole('button', { name: 'DSD Off Invoice (01)' }).click();
                
                await expect(this.page.getByText('One Allowance: DSD Combined')).toBeVisible();
                await this.page.locator('label').filter({ hasText: 'One Allowance: DSD Combined' }).getByTestId('radio-check').click();
                
                await expect(this.page.getByText('Allowance Dates')).toBeVisible();
            }
        }
    } catch (error) {
        console.log('Label element not found, proceeding with Performance dropdown...');
        // Directly try the Performance dropdown path
        await expect(this.page.getByText('Performance*')).toBeVisible();
        await this.page.waitForLoadState("networkidle");

        // Click the Performance dropdown
        await this.page.getByRole('button', { name: 'Performance' }).click();
        await this.page.waitForLoadState("networkidle");

        await this.page.getByRole('button', { name: 'DSD Off Invoice (01)' }).click();
        await this.page.waitForLoadState("networkidle");

        await expect(this.page.getByText('One Allowance: DSD Combined')).toBeVisible();
        await this.page.locator('label').filter({ hasText: 'One Allowance: DSD Combined' }).getByTestId('radio-check').click();
        
        await expect(this.page.getByText('Allowance Dates')).toBeVisible();
    }

    await this.page.getByRole('button', { name: 'Review Overlaps & Enter' }).click();
    await this.page.getByRole('spinbutton').fill('1');
    await this.page.getByRole('button', { name: 'Update All Divisions' }).click();

    await this.page.getByRole('button', { name: 'Save All Changes' }).click();
    await this.page.waitForURL('**/events/edit/**');
    await this.page.getByRole('button', { name: 'Select' }).click();
    await this.page.getByText('Invoice').click();

    await this.page.getByRole('textbox', { name: 'Enter Vendor Comments' }).fill('Testing flow');
    await this.page.getByRole('button', { name: 'Apply' }).click();
    await this.page.getByRole('button', { name: 'Edit Division Billing Info' }).click();
    await this.page.getByRole('button', { name: 'Expand All' }).click();
    await this.page.getByRole('button', { name: 'Collapsed All' }).click();

    await this.page.locator('input[name="vendorOfferTrackingNbr"]').fill('11002277546');
    await this.page.getByRole('button', { name: 'Save & Create Allowance(s)' }).click();

    await this.page.getByRole('button', { name: 'View Offer Deal Sheet' }).click();
    await this.page.getByTestId('close-icon-div-id').locator('path').click();

    await this.page.getByRole('button', { name: 'Actions' }).click();
    await this.page.getByRole('button', { name: 'Send to Vendor' }).click();
    await expect(this.page.locator('#event-header').getByText('pending with vendor')).toBeVisible();
  }


  // async offerCreation() {
    
  // await expect(page.getByText('New Offer')).toBeVisible();
  // await expect(page.getByText('Allowance Type')).toBeVisible();
  // await page.getByRole('button', { name: 'Scan' }).click();
  // await page.locator('div').filter({ hasText: /^Case$/ }).nth(2).click();
  // await expect(page.getByRole('button', { name: 'Off Invoice (01)' })).toBeVisible();
  // await expect(page.getByTestId('allowance-to-be-created-content-input').locator('div').nth(1)).toBeVisible();
  // await expect(page.getByText('Warehouse Only')).toBeVisible();
  // await expect(page.getByText('Vehicle Type/Custom Date')).toBeVisible();

  // await expect(page.getByText('Year')).toBeVisible();
  // await expect(this.page.getByRole('button', { name: '2025', exact: true })).toBeVisible();

  // }


}
