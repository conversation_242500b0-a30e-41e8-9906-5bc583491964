import fs from "fs";

let configCache: Record<string, any> = {};
interface EnvConfig {
  baseUrl: string;
  authStateFile: string;
  uri: string;
  dbName: string;
  auth: {
    "nationalMerchant": {
      username: string;
      password: string;
    },"divisionalMerchant": {
      username: string;
      password: string;
    },"nationalVendor": {
      username: string;
      password: string;
    },"divisionalVendor": {
      username: string;
      password: string;
    }
  }

}

function loadConfig(): Record<string, EnvConfig> {
  if (Object.keys(configCache).length === 0) {
    const configPath = "./config/config.json";
    if (!fs.existsSync(configPath)) {
      throw new Error(`Config file not found at path: ${configPath}`);
    }
    try {
      configCache = JSON.parse(fs.readFileSync(configPath, "utf-8"));
    } catch (err) {
      throw new Error(`Failed to parse config.json: ${(err as Error).message}`);
    }
  }  
  return configCache as Record<string, EnvConfig>;
}

export function getConfig(env: string): EnvConfig {
  const config = loadConfig();
  const envConfig = config[env];
  if (!envConfig) {
    throw new Error(`Environment "${env}" is not defined in config.json`);
  }
  return envConfig;
}

/**
 * Get a nested config value safely (e.g., "auth.username")
 */
export function getConfigValue<T = any>(env: string, keyPath: string, defaultValue?: T): T {
  const config = getConfig(env);
  const keys = keyPath.split(".");
  let value: any = config;

  for (const key of keys) {
    if (value && typeof value === 'object' && key in value) {
      value = value[key];
    } else {
      if (defaultValue !== undefined) return defaultValue;
      throw new Error(`Missing config key "${keyPath}" in env "${env}"`);
    }
  }
  return value;
}
