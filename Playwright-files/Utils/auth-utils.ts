import { Page } from "@playwright/test";
import { LoginPage } from "../../Playwright-files/Pages/LoginFlow/login-page";
import { getConfigValue } from "../Utils/config-utils";
import fs from "fs";

const env = process.env.TEST_ENV || "dev";
const baseUrl = getConfigValue(env, "baseUrl");
const authStateFile = getConfigValue(env, "authStateFile");
const username = getConfigValue(env, "auth.nationalMerchant.username");
const password = getConfigValue(env, "auth.nationalMerchant.password");

/**
 * Logs in and saves the storage state to authStateFile
 */
export async function regenerateAuthState(page:any, context:any) {
  console.log("Session expired. Regenerating auth state...");

  const loginPage = new LoginPage(page);
  await loginPage.navigate(baseUrl);
  await loginPage.login(username, password);

  await context.storageState({ path: authStateFile });
  console.log(`New auth state saved to ${authStateFile}.`);
}

/**
 * Clears the saved auth state file
 */
export function clearAuthState() {
  if (fs.existsSync(authStateFile)) {
    console.log(`Clearing auth state: ${authStateFile}`);
    fs.writeFileSync(
      authStateFile,
      JSON.stringify({ cookies: [], origins: [] }, null, 2)
    );
  }
}

/**
 * Validates that the auth state is still usable
 */
export async function isAuthStateValid(options: { page: Page }) {
  const { page } = options;

  if (!fs.existsSync(authStateFile)) {
    console.log(`${authStateFile} does not exist.`);
    return false;
  }

  try {
    console.log(`Validating auth state by navigating to: ${baseUrl}`);
    await page.goto(baseUrl);
    await page.waitForLoadState("networkidle");

    const errorVisible = await page
      .locator(
        "text=You do not have permission to access the Albertsons Partner Portal"
      )
      .isVisible();

    if (errorVisible) {
      console.log(
        "Authentication state is invalid: Permission error detected."
      );
      return false;
    }

    console.log("Authentication state is valid.");
    return true;
  } catch (error) {
    console.error("Error while validating auth state:", error);
    return false;
  }
}
