import { addDays, getISOWeek } from 'date-fns';

export  function getFutureDate(daysFromToday: number): Date {
    return addDays(new Date(), daysFromToday);
}

export  function getWeekFromDate(date: Date): number {
    return getISOWeek(date); // ISO week number
}


export function getVehicleInsertValueString(eventDate:string): string {
    // const eventDate = 'future~15';
    const daysFromToday = parseInt(eventDate.split('~')[1], 10);

    const futureDate = addDays(new Date(), daysFromToday);
    const weekNumber = getISOWeek(futureDate);
    const year = futureDate.getFullYear();

    const weekName = weekNumber < 10 ? `0${weekNumber}` : `${weekNumber}`;
    const month = (futureDate.getMonth() + 1).toString().padStart(2, '0');
    const day = futureDate.getDate().toString().padStart(2, '0');

    const formattedDate = `${month}/${day}/`;
    return `Week ${weekName} Insert ${year}`;
}
