

 interface ActionsInterface {
    getByRole(field: string): Promise<void>;
    getByLabel(field: string): Promise<void>;

 }
export interface ActInterface {
    click(props?: { button?: 'left' | 'right' | 'middle', delay?: number, force?: boolean }): Promise<void>;
    doubleClick(): Promise<void>;
    fill(value: string): Promise<void>;
    check(): Promise<void>;
    hover(): Promise<void>;
    selectOption(value: string | string[]): Promise<void>;
    selectOptionByLabel(label: string): Promise<void>;
}
const   Act = class   {
    
    static get locator() {
        return globalThis.page.locator;
    }
    static async click(props) {
        if(props){
            return this.locator.click(props);
        }
        return this.locator.click();
    }
   static async doubleClick() {
        return this.locator.dblclick();          
   }    
    static async fill( value: string) {
        return this.locator.fill(value);
    }

    static async check() {
        return this.locator.check();
    }
  static async hover() {
        return this.locator.hover();
    }
    static async selectOption( value: string | string[]) {
        return this.locator.selectOption(value);
    }
    static async selectOptionByLabel(label: string) {
        return this.locator.selectOption({ label: label });
    }
}




export default Act;