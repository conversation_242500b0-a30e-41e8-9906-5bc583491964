import Act from "./Act/Act";
import Arrange from "./Arrange/Arrange";
import Assertions from "./Assertion/Assertions";

class BaseActOperation extends Act{
    // You can add additional methods or properties specific to Act operations here
    constructor(...args: any[]) {
        super();
        // Additional initialization if needed
    }
}
class BaseAssertOperation extends Assertions {
    constructor(...args: any[]) {
        super();
        // Additional initialization if needed
    }
}
class BaseArrangeOperation extends Arrange {
    constructor(...args: any[]) {
        super();
        // Additional initialization if needed
    }
}


type Constructor<T = {}> = new (...args: any[]) => T;

type UnionToIntersection<U> = 
    (U extends any ? (k: U) => void : never) extends (k: infer I) => void ? I : never;

function mixin<TBases extends Constructor[]>(...bases: TBases) {
    class MixedClass {
        constructor(...args: any[]) {
            for (const Base of bases) {
                Object.assign(this, new Base(...args));
            }
        }
    }
    bases.forEach(Base => {
        Object.getOwnPropertyNames(Base.prototype).forEach(name => {
            if (name !== 'constructor') {
                Object.defineProperty(
                    MixedClass.prototype,
                    name,
                    Object.getOwnPropertyDescriptor(Base.prototype, name)!
                );
            }
        });
    });
    return MixedClass as Constructor<UnionToIntersection<InstanceType<TBases[number]>>>;
}
class BaseOperation extends mixin(BaseActOperation, BaseAssertOperation, BaseArrangeOperation) {
  constructor(...args: any[]) {
    super(...args);
    // Additional initialization if needed
  }
}