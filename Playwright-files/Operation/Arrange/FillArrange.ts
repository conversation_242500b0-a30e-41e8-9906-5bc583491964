import { Lo<PERSON><PERSON>, <PERSON> } from '@playwright/test';
import Arrange from "./Arrange";

 const FillArrange = class{
    async fillInputFieldByRole(field: string, value: string) {
        return Arrange.getByRole(field).fill(value);
    }

    async fillInputFieldByLabel(field: string, value: string) {
        return Arrange.getByLabel(field).fill(value);
    }
};
 
export default FillArrange;