import { Locator, Page } from '@playwright/test';

 const Arrange = class {
    static #byRole: Locator;
    static #byLabel: Locator;
    static #byText: Locator;
    static #byPlaceholder: Locator;
    
   static get page(){
    return globalThis.page;
   }
   static set setByRole (role){
        this.#byRole = this.page.getByRole(role);

    }
    static set setByLabel (role){
        this.#byLabel = this.page.getByLabel(role);

    }
    static set setByText (role){
        this.#byText = this.page.getByText(role);

    }
    static set setByPlaceholder (role){
        this.#byPlaceholder = this.page.getByPlaceholder(role);

    }

    static  getByLabel(label){
         this.setByLabel(label)
        return this.#byLabel;
    }
    static  getByRole(role){
        this.setByRole(role)
        return this.#byRole;
    }
  static  getByText(text){
        this.setByText(text)
        return this.#byText;    

    }

    static  getByPlaceholder(placeholder){
        this.setByPlaceholder(placeholder)
        return this.#byPlaceholder;    

    }
 }


export default Arrange;