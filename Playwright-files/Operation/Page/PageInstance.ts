
const PageInstance = class  {
   
   static get page(){
    return globalThis.page;
   }
   static waitForNavigation(){
    return this.page.waitForNavigation();
   }
  static  addInitScript(path){
      this.page.addInitScript({path});
   }
   static waitForSelector(selector,props){
    if(props){
        return this.page.waitForSelector(selector, props);
    }
    return this.page.waitForSelector(selector);
   }
   static waitForLoadState(state) {
    return this.page.waitForLoadState(state);
   }
   static goto(url: string) {
    return this.page.goto(url);
    }
    static waitForURL(url: string) {
    return this.page.waitForURL(url);
    }
   static getByAltText(field: string) {
    return this.page.getByAltText(field);
   }
    static getByLabel(field: string) {
     return this.page.getByLabel(field);
    }
    static getByRole(field: any,props?) {
        if(props){
            return this.page.getByRole(field, props);
        }
     return this.page.getByRole(field);
    }
    static getByText(field: string) {
     return this.page.getByText(field);
    }       
    static getByTestId(field: string) {
     return this.page.getByTestId(field);
    }

    static getByPlaceholder(field: string) {
     return this.page.getByPlaceholder(field);       
    }
    static getByTitle(field: string) {
     return this.page.getByTitle(field);       
    }   
    static clickByRole(field,props?,fillValue?: string) {
        let fieldLocator;
        if(props){
             fieldLocator = this.getByRole(field,props);
        }else{
            fieldLocator = this.getByRole(field);
        }

    return fieldLocator.click();

    }
    static fillByRole(field,props?,fillValue?: string) {
        let fieldLocator;
        if(props){
             fieldLocator = this.getByRole(field,props);
        }else{
            fieldLocator = this.getByRole(field);
        }
       
     return fieldLocator.fill(fillValue);

    }
     static fillByLocator(selector,fillValue) {
        const fieldLocator = this.locator(selector);
      fieldLocator.fill(fillValue);

    }
     static clickByText(field) {
        const fieldLocator = this.getByText(field)
     return fieldLocator.click();

    }
    static clickByLocator(selector: string) {
     const fieldLocator = this.locator(selector);
        return fieldLocator.click();
    }
    static get locator() {
     return this.page.locator;
    }

}

export default PageInstance;
