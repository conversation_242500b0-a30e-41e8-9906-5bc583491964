import { Locator, Page ,expect } from '@playwright/test';
import Arrange from '../Arrange/Arrange';
import Act from '../Act/Act';


const Assertions  = class{ 
   
   
    static get page(){
    return globalThis.page;
   }
   static get locator() {
        return this.page.locator;
    }
    static getPagetLocatorByText(locator: any) {
        return this.page.getByText(locator) 
    }
    static getPagetLocatorByPlaceholder(locator: any) {
        return this.page.getByPlaceholder(locator) 
    }
    static getPagetLocatorByLabel(locator: any) {
        return this.page.getByLabel(locator) 
    }
    static getPagetLocatorByRole(locator: any,props) {
        return this.page.getByRole(locator,props) 
    }
    static getByTestId(locator: any) {      
        return this.page.getByTestId(locator) 
    }
    static getPagetLocator(locator: any) {
        return this.page.locator(locator) 
    }
   static toBeAttached (locator:any){
    //await expect(page.getByText('Hidden text')).toBeAttached();
    const pageLocator = this.getPagetLocatorByText(locator) 
    expect(pageLocator).toBeAttached();
   }
    static async toContainClass(locator,value){
        const pageLocator = this.getPagetLocator(locator) 
        await expect(pageLocator).toContainClass(value);
    }
    static async toContainText (locator,value){   
        const pageLocator = this.getPagetLocator(locator) 
        await expect(pageLocator).toContainText(value);

    }
 static async toBeChecked (locator :any){
    //const locator = page.getByLabel('Subscribe to newsletter');
    const pageLocator = this.getPagetLocatorByLabel(locator) 
    await expect(this.locator).toBeChecked();

    }
    static async toBeDisabled (locator :any){
        //const locator = page.locator('button.submit');
        const pageLocator = this.getPagetLocator(locator) 

        await  expect(pageLocator).toBeDisabled();

    }
    static async  toBeEditable (locator:any,props){
        //const locator = page.getByRole('textbox');
        const pageLocator = this.getPagetLocatorByRole(locator,props) 

        await   expect(pageLocator).toBeEditable();

    }
    static async toBeEmpty (locator:any){
        //const locator =   p age.locator('div.warning');   
          const pageLocator = this.getPagetLocator(locator)

        await   expect(pageLocator).toBeEmpty();

    }
    static async  toBeEnabled (locator:any){
        //const locator = page.locator('button.submit');
        const pageLocator = this.getPagetLocator(locator)

        await  expect(this.locator).toBeEnabled();

    }
    static async toBeFocused (locator:any,props){
        //const locator = page.getByRole('textbox');
        const pageLocator = this.getPagetLocatorByRole(locator,props)
        await  expect(this.locator).toBeFocused();

    }       
    static async toBeHidden (locator:any){
        //const locator = page.locator('.my-element');
        const pageLocator = this.getPagetLocator(locator)

        await  expect(this.locator).toBeHidden();

    }   
    static async toBeInViewport (locator:any,value,props){
    const pageLocator = this.getPagetLocatorByRole(locator,props);
// Make sure at least some part of element intersects viewport.
    if(!value){
        await expect(pageLocator).toBeInViewport();
    }else{
        await expect(pageLocator).toBeInViewport({ [value]:value });
    }

    }
    static async toBeNotInViewport (locator:any,props){
        const pageLocator = this.getPagetLocatorByRole(locator,props);
            await expect(pageLocator).not.toBeInViewport();
    
        }
    static async toBeVisible (locator:any,method){
        let pageLocator = this.getPagetLocatorByText(locator);
        if(method){
             pageLocator = this.getPagetLocatorByText(locator);
            await expect(this.getByTestId(pageLocator)?.[`${method}`]()).toBeVisible();
        }else{
            await expect(this.getPagetLocatorByText(locator)).toBeVisible();
        }
    }

    static async toBe (value:any){
        await expect(value).toBe(value);  
    }
    static async toBeCloseTo (value:any){   
        
        await expect(value).toBeCloseTo(value);  
    }
    static async toBeDefined (value:any){
        await expect(value).toBeDefined();  
    }
    static async toBeFalsy (value:any){
        await expect(value).toBeFalsy();  
    }
    static async toBeGreaterThan (value:any){
        await expect(value).toBeGreaterThan(value);  
    }
    static async toBeGreaterThanOrEqual (value:any){
        await expect(value).toBeGreaterThanOrEqual(value);  
    }
    static async toBeInstanceOf (instance,value:any){
        await expect(instance).toBeInstanceOf(value);  
    }
    static async toBeLessThan (check,value:any){
        await expect(check).toBeLessThan(value);  
    }
    static async toBeLessThanOrEqual (check,value:any){
        await expect(check).toBeLessThanOrEqual(value);  
    }
    static async toBeNaN (value:any){
        await expect(value).toBeNaN();  
    }   
    static async toBeNull (value:any){
        await expect(value).toBeNull();  
    }
    static async toBeTruthy (value:any){
        await expect(value).toBeTruthy();  
    }
    static async toBeUndefined (value:any){
        await expect(value).toBeUndefined();  
    }
    static async toContain (contains,value:any){
        await expect(value).toContain(contains);  
    }
    static async toContainEqual (contains,value:any){
        await expect(value).toContainEqual(contains);  
    }
    static async toEqual (equal,value:any){       
        await expect(equal).toEqual(value);  
    }
    static async toHaveLength (value:any,length:any){       
        await expect(value).toHaveLength(length);  
    }
    static async toHaveProperty (value:any,props:any){
        await expect(value).toHaveText(props);  
    }
    static async toMatch (value:any,text:any){
        await expect(value).toMatch(text);  
    }
    static async toMatchObject (value:any,object:any){
        await expect(value).toMatchObject(object);  
    }
    static async toStrictEqual (value:any,object:any){
        await expect(value).toStrictEqual(object);  
    }
    static async toThrow (functionProp:any){
        await expect(functionProp).toThrow();  
    }
    static async toThrowError (functionProp:any){
        await expect(functionProp).toThrowError();  
    }
}           

export default Assertions;