 const  eventCreationData = {
    ppg: {
      label: 'Promo Product Groups',
      value: 'Anna\'s Swedish Thins 5.25-oz.; CIG:',
      id: '695777 - <PERSON>\'s Swedish Thins',
    },
    division: {
      name: 'Division',
      id: '05-DENVER',
    },
    ViewPPGStores: {
      label: 'View Items',
      itemsCount: '1',
    },
    storeGroupType: {
      label: 'Store Group Type',
      type: 'Division',
    },
    storeGroups: {
      label: 'Store Groups',
      type: 'Division',
      viewStoresText: 'Denver All Stores (126)',
      totalStores: 126,
    },
    vehicleType: {
      option: 'Promo Cycle', // Options: 'Other', 'Promo Cycle', 'Custom Date'
      startWeek: '05 3wk Feature Wk 34 2025 -',
      startDate: '08/06/25',
      endDate: '09/02/25',
      week: '05 3wk Feature Wk 34 2025 -',
    },
    year: {
      label: 'Year',
      value: '2025',
    },
  }

    const  nationalEventCreationData = {
    storeGroup: {
      label: 'Store Group Type',
      type: 'National',
      value: /13 Divisions selected/,
      totalStores: 2302,
    },
    dsdPpg: {
      label: 'Promo Product Groups',
      placeholder: 'Search Promo Product Groups',
      value: 'ALTER ECO CHOCOLATE CIG',
      id: '823765 - ALTER ECO CHOCOLATE',
    },
    warehousePpg: {
      label: 'Promo Product Groups',
      placeholder: 'Search Promo Product Groups',
      value: 'Yankee Medium Pillar Candles 14.25oz CIG 247854',
      id: '822830 - Yankee Medium Pillar Candles',
    }
  }

  
   const PIDEventCreationData = {
    pid: {
      label: 'PID',
      value: '60740',
      fetchPID: 'Fetch Event Details', 
    },
    eventName: /GM- ROC RETINOL CORREXION LINE SMOOTHING MAX CIG 513699/,
    storeGroup: {
      label: 'Store Group Type',
      division: 'National',
      value: /11 Divisions selected/,
    },
    vehicleType: {
      option: 'Promo Cycle', // Options: 'Other', 'Promo Cycle', 'Custom Date'
      week: 'GMHBC Dec Feature 2024',
      year: {
        label: 'Year',
        value: '2024',
      },
      startDate: '12/04/24',
      endDate: '01/02/25',
    }
  }

  export  {  eventCreationData,nationalEventCreationData, PIDEventCreationData }