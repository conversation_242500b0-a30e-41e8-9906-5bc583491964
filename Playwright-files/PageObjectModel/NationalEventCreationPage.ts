import {expect, Page} from "@playwright/test";
import { VehicleComponent } from "../../src/ui/Components/baseComponent/EventFields/Vehicle/VehicleComponent";
import { StoreGroupComponent } from "../../src/ui/Components/baseComponent/EventFields/StoreGroup/StoreGroupComponent";
import { ProductGroupComponent } from "../../src/ui/Components/baseComponent/EventFields/ProductGroup/ProductGroupComponent";

export class NationalEventCreationPage {
    private storeGroup: StoreGroupComponent;
    private productGroup: ProductGroupComponent;
    private vehicle: VehicleComponent;

    constructor(private page: Page) {
        this.storeGroup = new StoreGroupComponent(page);
        this.productGroup = new ProductGroupComponent(page);
        this.vehicle = new VehicleComponent(page);
    }

    async populateStoreGroupsData(storeGroupData:any) {
        try {
            // First get the store group type
            if (storeGroupData?.type) {
                await this.storeGroup.getStoreGroupType({
                    label: "Store Group Type",
                    type: storeGroupData.type
                });
            }

            // Then get store groups data
            if (storeGroupData?.value) {
                await this.storeGroup.getStoreGroups({
                    label: storeGroupData.value,
                    // Only pass totalStores if it exists
                    ...(storeGroupData.totalStores && { totalStores: storeGroupData.totalStores })
                });
            }
        } catch (error:any) {
            console.error('Error in populateStoreGroupsData:', error);
            throw new Error(`Failed to populate store group data: ${error.message}`);
        }
    }

    async populateProductGroupNational(ppgData:any) {
        await this.productGroup.selectProductGroup(ppgData);
    }

    async populateVehicleTypeOrCustomDateNational(option: 'Other' | 'Promo Cycle' | 'Custom Date') {
        try {
            // Select vehicle type
            await this.vehicle.selectVehicleType(option);

            if (option === 'Promo Cycle') {
                // For Promo Cycle, select the start week and verify dates
                const weekData = '4wk Feature Wk 35 2025 - 08/27/25 - 09/23/25';
                await this.vehicle.selectStartWeek(weekData);
                await this.vehicle.verifyStartDate('08/27/25');
                await this.vehicle.verifyEndDate('09/23/25');
            } else if (option === 'Custom Date') {
                // For Custom Date, dates are auto-selected in the component
                const dropdown = this.page.getByRole('button', { name: 'Start Week/Vehicle' });
                await expect(dropdown).toBeVisible();
                await expect(dropdown).toHaveText(/Other/);
            }
        } catch (error:any) {
            console.error('Error in populateVehicleTypeOrCustomDateNational:', error);
            throw new Error(`Failed to populate vehicle type: ${error.message}`);
        }
    }

    async saveEventDetails() {
        await this.page.getByRole('button', { name: 'Save Event Details & Add' }).click();
        // await this.page.waitForLoadState('networkidle');
        // const alert = this.page.getByRole('alert');
        // if (await alert.isVisible()) {
        //     await expect(alert).toBeVisible();
        //     await expect(alert.getByRole('img')).toBeVisible();
        //     await expect(this.page.getByText('The following divisions do')).toBeVisible();
        //     await expect(this.page.getByRole('button', { name: 'Remove Invalid Divisions' })).toBeVisible();

        await expect(this.page.getByRole('alert')).toBeVisible();
        await expect(this.page.getByRole('alert').getByRole('img')).toBeVisible();
        await expect(this.page.getByText('The following divisions do')).toBeVisible();
        await expect(this.page.getByRole('button', { name: 'Remove Invalid Divisions' })).toBeVisible();
        await this.page.getByRole('button', { name: 'Remove Invalid Divisions' }).click();
        await expect(this.page.getByRole('alert')).not.toBeVisible();
        await this.page.getByRole('button', { name: 'Save Event Details & Add' }).click();
    // }
}
}
