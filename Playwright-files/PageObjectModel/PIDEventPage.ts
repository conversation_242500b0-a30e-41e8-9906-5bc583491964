import { Page, expect } from '@playwright/test';

export class PIDEventPage {
  constructor(private page: Page) {}


  async clickAddEvent() {
    await this.page.getByRole('link', { name: 'Add Event' }).click();
  }

  async startNationalEventFlow() {
    await this.page.getByRole('button', { name: 'Get Started' }).nth(2).click();
  }

  async enterPID(pid: string) {
    const pidInput = this.page.getByRole('spinbutton');
    await pidInput.click();
    await pidInput.fill(pid);
  }

  async fetchEventDetails() {
    await this.page.getByRole('button', { name: 'Fetch Event Details' }).click();
  }

  async verifyInvalidPIDError() {
    await expect(this.page.getByText('This PID is invalid')).toBeVisible();
  }
}
