import { Page, expect } from '@playwright/test';

export class SearchByEventIdPage {
  constructor(private page: Page) {}


  async selectEventIdFilter() {
    await this.page.getByRole('button', { name: 'Offer ID#' }).click();
    await this.page.getByText('Event ID#').click();
  }

  async selectPlanningTasks() {
    await this.page.getByRole('button', { name: 'Tasks' }).click();
    await this.page.getByText('Planning').click();
  }

  async searchByEventId(eventId: string) {
    await this.page.getByRole('textbox', { name: 'Search' }).click();
    await this.page.getByRole('textbox', { name: 'Search' }).fill(eventId);
    await this.page.locator('div:nth-child(4) > .lucide').click();
   await this.page.getByRole('link', { name: new RegExp(`${eventId} -`, 'i') }).first().click();
  }
}
