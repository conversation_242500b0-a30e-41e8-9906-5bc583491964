import { Page } from '@playwright/test';

export class DivisionUserDraftStatusPage {
  readonly page: Page;

  constructor(page: Page) {
    this.page = page;
  }


  async filterByPlanningTask() {
    await this.page.getByRole('button', { name: 'Tasks' }).click();
    await this.page.getByText('Planning', { exact: true }).click();
  }

  async selectSearchType(searchType: string) {
    await this.page.getByRole('button', { name: /ID#/ }).click();
    await this.page.getByText(searchType, { exact: true }).click();
  }

  async performSearch(value: string) {
    const searchBox = this.page.getByRole('textbox', { name: 'Search' });
    await searchBox.click();
    await searchBox.fill(value);
    await this.page.locator('div:nth-child(4) > .lucide').click();
  }
}
