import { Page, expect } from '@playwright/test';

export class NationalUserDraftVisiblePage {
  constructor(private page: Page) {}


  async performSearchAndNavigation() {
    await this.page.getByRole('button', { name: 'Offer ID#' }).click();
    await this.page.getByText('Event ID#').click();
    await this.page.getByRole('button', { name: 'Tasks' }).click();
    await this.page.getByText('Planning').click();
    await this.page.locator('#abs-multi-select-dropdown0').getByRole('img').click();
    await this.page.locator('#abs-multi-select-dropdown2').getByRole('img').click();
    await this.page.locator('#abs-multi-select-dropdown3 > div > div > label > div > .flex').first().click();
    await this.page.getByRole('textbox', { name: 'Search' }).click();
    await this.page.getByRole('textbox', { name: 'Search' }).fill('10058822');
    await this.page.locator('div:nth-child(4) > .lucide').click();
    await this.page.getByTestId('link-by-product-d').click();

  }
}
