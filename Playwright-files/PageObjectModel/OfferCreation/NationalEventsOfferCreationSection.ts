// import { Page, expect, Locator } from '@playwright/test';
// import { NationalEventsOfferCreationSectionPageEnum } from '../../../src/Enums/NationalEventsOfferCreationSectionPageEnum';
// import { pageFixture } from '../../../config/pageFixtures';
// import { nationalEventCreationData } from '../../DynamicConfiguration/Event/DivsionOnly/createEventConfig';
// import { getVehicleInsertValueString } from '../../../src/Utils/CommonUtils';

// export class NationalEventsOfferCreationSection {

//     readonly page: Page;
//     readonly offerHeader: Locator;
//     readonly allowanceTypeDropdown: Locator;
//     readonly allowanceToBeCreatedContentInputLabelForCaseOfferFlow: Locator;
//     readonly allowanceDatesHeader: Locator;
//     readonly warehouseAllowanceDatesHeader: Locator;
//     readonly dsdAllowanceDatesHeader: Locator;
//     readonly bothRadioButton: Locator;
//     readonly warehouseOnlyRadioButton: Locator;
//     readonly dsdOnlyRadioButton: Locator;
//     readonly warehouseOnlyRadioButtonDefaultCheckedAndDisabled: Locator;
//     readonly combinedDsdRadioButton: Locator;
//     readonly separateAllowancesByDsdRadioButton: Locator;
//     readonly okButtonOnAllowAmountMainEntryScreenOneTimePopupMessage: Locator;
//     readonly enterAmountsButton: Locator;
//     readonly allowanceMainEntryScreenAmountsTextBox: Locator;
//     readonly updateAllDivisionsButton: Locator;
//     readonly saveAllChangesButton: Locator;
//     readonly continueToWarehouseDatesButton: Locator;
//     readonly saveAndCreateAllowancesButton: Locator;
//     readonly reviewOverlapsAndEnterAmountsButton: Locator;
//     readonly allowanceToBeCreatedFieldsValue: Locator;
//     readonly viewOfferDealSheetButton: Locator;
//     readonly viewSummaryButton: Locator;
//     readonly headerFlatAllowanceAmountTextBox: Locator;
//     readonly continueToBillingDetails: Locator;
//     readonly headerFlatAllowanceTypeValue: Locator;
//     readonly viewSummaryModalHeader: Locator;
//     readonly shipStore: Locator;
//     readonly oneAllowanceWarehouseDSDradioOption: Locator;
//     readonly separateAllowancesByDSDradioOption: Locator;
//     readonly defaultSelectValueDisplayInPerformanceDropdown: Locator;   
//     readonly fourUEventPerformanceValue: Locator;
//     readonly addEvent: Locator;
//     readonly getStartedNational;
//     readonly ppgDropdown: Locator;
//     readonly searchPPG: Locator;
//     readonly saveEventDetails: Locator;
//     readonly removeInvalidDivisions: Locator;
//     readonly eventId: Locator;
//     readonly selectsVehicleType: Locator;
//     readonly selectStartWeekVehicle: Locator;

//     constructor(page: Page) {
//         this.page = page;
//         this.offerHeader = this.page.getByText('New Offer');
//         this.allowanceTypeDropdown = this.page.locator('//div[@id="abs-input-select-container"]/button[@name="allowanceType"]');
//         this.allowanceToBeCreatedContentInputLabelForCaseOfferFlow = this.page.locator("[data-testid='allowance-to-be-created-content-input'] label");
//         this.allowanceDatesHeader = this.page.getByText('Allowance Dates');
//         this.warehouseAllowanceDatesHeader = this.page.getByText('Warehouse Allowance Dates');
//         this.dsdAllowanceDatesHeader = this.page.getByText('DSD Allowance Dates');
//         this.bothRadioButton = this.page.locator('//span[@data-testid="radio-check"]').nth(0);
//         this.warehouseOnlyRadioButton = this.page.locator('//span[@data-testid="radio-check"]').nth(1);
//         this.dsdOnlyRadioButton = this.page.locator('//span[@data-testid="radio-check"]').nth(2);
//         this.warehouseOnlyRadioButtonDefaultCheckedAndDisabled = this.page.getByTestId('radio-check');
//         this.combinedDsdRadioButton = this.page.locator('label').filter({ hasText: 'Combined DSD' }).getByTestId('radio-check');
//         this.separateAllowancesByDsdRadioButton = this.page.locator('label').filter({ hasText: 'Separate Allowances By DSD' }).getByTestId('radio-check');
//         this.okButtonOnAllowAmountMainEntryScreenOneTimePopupMessage = this.page.locator(`//button[text()="OK"]`);
//         this.enterAmountsButton = this.page.getByRole('button', { name: 'Enter Amounts' });
//         this.allowanceMainEntryScreenAmountsTextBox = this.page.getByRole('spinbutton');
//         this.updateAllDivisionsButton = this.page.getByRole('button', { name: 'Update All Divisions' });
//         this.saveAllChangesButton = this.page.getByRole('button', { name: 'Save All Changes' });
//         this.saveAndCreateAllowancesButton = this.page.getByRole('button', { name: 'Save & Create Allowance(s)' });
//         this.continueToWarehouseDatesButton = this.page.getByRole('button', { name: 'Continue to Warehouse Dates' });
//         this.reviewOverlapsAndEnterAmountsButton = this.page.getByRole('button', { name: 'Review Overlaps & Enter amounts' });
//         this.allowanceToBeCreatedFieldsValue = this.page.locator('(//div[@id="abs-stepper-preview-header1"]/div/div/span)[2]');
//         this.viewOfferDealSheetButton = this.page.getByRole('button', { name: 'View Offer Deal Sheet' });
//         this.viewSummaryButton = this.page.getByText('View Summary');
//         this.headerFlatAllowanceAmountTextBox = this.page.locator('input[name="hfHeader\\.0"]');
//         this.continueToBillingDetails = this.page.getByRole('button', { name: 'Continue to Billing Details' });
//         this.headerFlatAllowanceTypeValue = this.page.locator('(//div[@id="abs-stepper-preview-header1"]/div/div/span)[1]');
//         this.viewSummaryModalHeader = this.page.getByText('Summary: Allowance Amounts |');
//         this.shipStore = this.page.getByText('Ship To Store');
//         this.oneAllowanceWarehouseDSDradioOption = this.page.locator('label').filter({ hasText: 'One Allowance: Warehouse, DSD' }).getByTestId('radio-check');
//         this.separateAllowancesByDSDradioOption = this.page.locator('label').filter({ hasText: 'Separate Allowances By DSD' }).getByTestId('radio-check');
//         this.defaultSelectValueDisplayInPerformanceDropdown = this.page.getByRole('button', { name: 'Select' });
//         this.fourUEventPerformanceValue = this.page.getByText('4U Event (52)');
//         this.addEvent = this.page.getByRole('link', { name: 'Add Event' });
//         this.getStartedNational = this.page.getByRole('button', { name: 'Get Started' });
//         this.ppgDropdown = this.page.locator('#abs-input-auto-complete');
//         this.searchPPG = this.page.getByRole('textbox', { name: 'Search Promo Product Groups' });
//         this.saveEventDetails = this.page.getByRole('button', { name: 'Save Event Details & Add' });
//         this.removeInvalidDivisions = this.page.getByRole('button', { name: 'Remove Invalid Divisions' });
//         this.eventId = this.page.getByText('ID #');
//         this.selectsVehicleType = this.page.getByRole('button', { name: 'Vehicle Type/Custom Date'});
//         this.selectStartWeekVehicle = this.page.getByRole('button', { name: 'Start Week/Vehicle' });
//     }

//     // This function verifies that the "New Offer" header is visible on the page.
//     async verifyNewOfferHeader(page: Page) {
//         if (await this.offerHeader.isVisible()) {
//             await this.offerHeader.waitFor({ state: 'visible', timeout: 10000 });
//             await expect(this.offerHeader).toBeVisible();
//         }

//     }

//     // This function clicks the "Allowance Type" dropdown in the first stepper of the allowance workflow.
//     async clickAllowanceTypeDropdown() {
//             await this.allowanceTypeDropdown.waitFor({ state: 'visible', timeout: 10000 });
//             await this.allowanceTypeDropdown.click();
//     }

//     // This function selects the allowance type from the dropdown in the first stepper of the allowance workflow.
//     async selectAllowanceType(allowanceType: string) {
//         const allowance = this.page.getByText(allowanceType);
//         if (await allowance.isVisible()) {
//             await allowance.click();
//         }
//     }

//     // This function ensures default selection of, or selects the radio button option in the first stepper of the allowance workflow based on the PPG source and label radio button option.
//     async ensureDefaultSelectionAtOrSelectStepperOneRadioButtonOptionForCaseOffer(ppgSource: string, labelRadioButtonOption: string) {
//           await this.page.waitForLoadState('domcontentloaded');
//         if (await this.allowanceToBeCreatedContentInputLabelForCaseOfferFlow.isVisible()) {
//             const labelText = await this.allowanceToBeCreatedContentInputLabelForCaseOfferFlow.innerText();
//             console.log("labelText>", labelText);
//           }

//         if(ppgSource === 'DSD') {
//             if(
//                 labelRadioButtonOption === NationalEventsOfferCreationSectionPageEnum.OneAllowanceDSDCombined ||
//                 labelRadioButtonOption === NationalEventsOfferCreationSectionPageEnum.SeparateAllowancesByDSDDistributor
//             ) {
//                 await this.page.getByText(labelRadioButtonOption).click();
//             }
//             await expect(this.allowanceDatesHeader).toBeVisible();
//         } else if( ppgSource === NationalEventsOfferCreationSectionPageEnum.Warehouse ) {
//             const radio = this.warehouseOnlyRadioButtonDefaultCheckedAndDisabled;
//             const isChecked = await radio.isChecked();
//             if (!isChecked) {
//                 await radio.click();
//             }
//             await expect(this.warehouseAllowanceDatesHeader).toBeVisible();
//         } else if(ppgSource === NationalEventsOfferCreationSectionPageEnum.WarehouseDSD) {
//             if(labelRadioButtonOption === NationalEventsOfferCreationSectionPageEnum.Both) {
//                 await this.bothRadioButton.click();
//                 await expect(this.dsdAllowanceDatesHeader).toBeVisible();
//             } else if(labelRadioButtonOption === NationalEventsOfferCreationSectionPageEnum.WarehouseOnly) {
//                 await this.warehouseOnlyRadioButton.click();
//                 await expect(this.warehouseAllowanceDatesHeader).toBeVisible();
//             } else if(labelRadioButtonOption === NationalEventsOfferCreationSectionPageEnum.DSDOnly) {
//                 await this.dsdOnlyRadioButton.click();
//                 await expect(this.dsdAllowanceDatesHeader).toBeVisible();
//             }
//         }
//     }
    
//     // This function clicks the "Enter amounts" button that appears in one of the steppers of the allowance workflow.
//     async clickEnterAmountsButton() {
//         await this.page.waitForLoadState('domcontentloaded');
//         if (await this.enterAmountsButton.isVisible()) {
//             await this.enterAmountsButton.click();
//         }
//     }

//     // This function clicks the "OK" button on the one-time popup message that appears over the allowance workflow's main entry screen on first entry of the allowance amount for any shared item during the session (create or edit flow).
//     async clickOkButtonOnOneTimePopupMessage(sharedItem: boolean) {
//         if(sharedItem) {
//             await this.okButtonOnAllowAmountMainEntryScreenOneTimePopupMessage.waitFor({ state: 'visible', timeout: 10000 });
//             await this.okButtonOnAllowAmountMainEntryScreenOneTimePopupMessage.click();
//         }
//     }

//     // This function enters the allowance amount on the main entry screen of the allowance workflow.
//     async enterAllowanceAmountOnMainEntryScreen(amount: string) {
//         await this.allowanceMainEntryScreenAmountsTextBox.fill(amount);
//     }

//     // This function clicks the "Update All Divisions" button on the main entry screen of the allowance workflow.
//     async clickUpdateAllDivisionsButton() {
//         if (await this.updateAllDivisionsButton.isVisible()) {
//             await this.updateAllDivisionsButton.click();
//         }
//     }

//     // This function clicks the "Save All Changes" button on the main entry screen of the allowance workflow.
//     async clickSaveAllChangesButton() {
//         if (await this.saveAllChangesButton.isVisible()) {
//             await this.saveAllChangesButton.click();
//         }
//     }

//     // This function waits for the event edit page to load after saving the event details.
//     async waitForEventEditPage() {
//         await this.page.waitForURL('**/events/edit/**');
//     }

//     // This function clicks the "Continue to Warehouse Dates" button that appears in one of the steppers of the allowance workflow.
//     async clickContinueToWarehouseDatesButton() {
//         await this.continueToWarehouseDatesButton.waitFor({ state: 'visible', timeout: 10000 });
//         await this.continueToWarehouseDatesButton.click();
//     }
     
//     // This function clicks the "Review Overlaps & Enter amounts" button that appears in one of the steppers of the allowance workflow.
//     async clickReviewOverlapsAndEnterAmountsButton() {
//         await this.reviewOverlapsAndEnterAmountsButton.waitFor({ state: 'visible', timeout: 10000 });
//         await this.reviewOverlapsAndEnterAmountsButton.click();
//     }

//     // This function clicks the "Save & Create Allowance(s)" button in the last stepper of the allowance workflow.
//     async clickSaveAndCreateAllowancesButton() {
//         await this.saveAndCreateAllowancesButton.waitFor({ state: 'visible', timeout: 10000 });
//         await this.saveAndCreateAllowancesButton.click();
//     }

//     // Depending on the PPG source and label radio button option, this function validates the "Allowance to be Created" field value in the preview header.
//     async allowanceToBeCreatedFieldsValueValidation(ppgSource: string, labelRadioButtonOption: string) {
//         if(ppgSource === NationalEventsOfferCreationSectionPageEnum.DSD && labelRadioButtonOption === NationalEventsOfferCreationSectionPageEnum.OneAllowanceDSDCombined || 
//         ppgSource === NationalEventsOfferCreationSectionPageEnum.DSD && labelRadioButtonOption === NationalEventsOfferCreationSectionPageEnum.SeparateAllowancesByDSDDistributor ||
//         ppgSource === NationalEventsOfferCreationSectionPageEnum.WarehouseDSD && labelRadioButtonOption === NationalEventsOfferCreationSectionPageEnum.Both ||
//         ppgSource === NationalEventsOfferCreationSectionPageEnum.WarehouseDSD && labelRadioButtonOption === NationalEventsOfferCreationSectionPageEnum.WarehouseOnly ||
//         ppgSource === NationalEventsOfferCreationSectionPageEnum.WarehouseDSD && labelRadioButtonOption === NationalEventsOfferCreationSectionPageEnum.DSDOnly) {

//         const spanText = await this.allowanceToBeCreatedFieldsValue.innerText();
//         expect(spanText.trim()).toBe(labelRadioButtonOption);
//         }
//         else if(ppgSource === NationalEventsOfferCreationSectionPageEnum.Warehouse) {
//             const spanText = await this.allowanceToBeCreatedFieldsValue.innerText();
//         expect(spanText.trim()).toBe('Warehouse Only');
//         }
//     }

//     async configureAllowance(option: NationalEventsOfferCreationSectionPageEnum.CombinedDSD | NationalEventsOfferCreationSectionPageEnum.SeparateAllowancesByDSDDistributor, amount: string = '1') {
//         try {
//             console.log(`Configuring allowance with option: ${option}`);
//             await this.allowanceTypeDropdown.isVisible();
//             await this.allowanceTypeDropdown.click();
            
//             if (await this.shipStore.isVisible()) {
//                 await this.shipStore.click();
//             }
//             if (option === NationalEventsOfferCreationSectionPageEnum.CombinedDSD) {
//                 await this.combinedDsdRadioButton.click();
//             } else if (option === NationalEventsOfferCreationSectionPageEnum.SeparateAllowancesByDSDDistributor) {
//                 await this.separateAllowancesByDsdRadioButton.click();
//             } else {
//                 throw new Error(`Invalid option provided: ${option}`);
//             }

//             await this.enterAmountsButton.click();
//             await this.allowanceMainEntryScreenAmountsTextBox.fill(amount);
//             await this.updateAllDivisionsButton.click();
//             await this.saveAllChangesButton.click();
//             await this.saveAndCreateAllowancesButton.click();

//             console.log('Allowance configuration completed.');
//         } catch (error) {
//             console.error('Error during allowance configuration:', error);
//             throw error;
//         }

//     }
//     async viewOfferDealSheet() {
//         await this.viewOfferDealSheetButton.waitFor({ state: 'visible', timeout: 10000 });
//         await this.viewOfferDealSheetButton.click();
//     }

//     async clickViewSummaryIcon() {
//         await this.viewSummaryButton.waitFor({ state: 'visible', timeout: 10000 });
//         await this.viewSummaryButton.click();
//     }

//     async clickHeaderFlatAllowanceAmountTextBox() {
//         await this.headerFlatAllowanceAmountTextBox.waitFor({ state: 'visible', timeout: 10000 });
//         await this.headerFlatAllowanceAmountTextBox.click();
//     }

//     async enterHeaderFlatAllowanceAmount(amount: string) {
//         await this.headerFlatAllowanceAmountTextBox.waitFor({ state: 'visible', timeout: 10000 });
//         await this.headerFlatAllowanceAmountTextBox.fill(amount);
//     }

//     async clickContinueToBillingDetailsButton() {
//         await this.continueToBillingDetails.waitFor({ state: 'visible', timeout: 10000 });
//         await this.continueToBillingDetails.click();
//     }

//     async verifyHeaderFlatOffersAllowanceType() {
//         // Wait for the element containing the allowance type to be visible
//         const allowanceTypeElement = this.headerFlatAllowanceTypeValue;
//         await expect(allowanceTypeElement).toBeVisible();
//         const allowanceType = await allowanceTypeElement.innerText();
//         expect(allowanceType).toContain('Header Flat');
//     }

//     async createScanAllowance(dsdType: string): Promise<void> {
//         await this.allowanceTypeDropdown.click();
//         await this.selectAllowanceType('Scan');
//         await this.defaultSelectValueDisplayInPerformanceDropdown.click();
//         await this.fourUEventPerformanceValue.click();

//         if (dsdType === NationalEventsOfferCreationSectionPageEnum.One) {
//             await expect(this.oneAllowanceWarehouseDSDradioOption).toBeVisible();
//             await this.oneAllowanceWarehouseDSDradioOption.click();
//         } else if (dsdType === NationalEventsOfferCreationSectionPageEnum.Separate) {       
//             await expect(this.separateAllowancesByDSDradioOption).toBeVisible();
//             await this.separateAllowancesByDSDradioOption.click();
//         } else {
//             throw new Error(`Invalid DSD type: ${dsdType}`);
//         }

//         await this.enterAmountsButton.click();
//         await this.allowanceMainEntryScreenAmountsTextBox.isVisible();
//         await this.allowanceMainEntryScreenAmountsTextBox.fill('1');
//         await this.updateAllDivisionsButton.click();
//         await this.saveAllChangesButton.click();
//         await this.saveAndCreateAllowancesButton.click();
//     }

//     async verifyAllowanceAmountIsVisible() {
//         await expect(this.viewSummaryModalHeader).toBeVisible();
//     }