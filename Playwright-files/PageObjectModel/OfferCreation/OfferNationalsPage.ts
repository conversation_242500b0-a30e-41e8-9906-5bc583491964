import { Page, expect } from '@playwright/test';
import { getConfig } from '../../../src/ui/Utils/config-utils';

export class OfferNationalsPage {
  constructor(private page: Page) { }


  async createAndSubmitNationalOffer() {
    const env = process.env.TEST_ENV || "dev";
    const { baseUrl } = getConfig(env);

    await expect(this.page.getByText('New Offer')).toBeVisible();
    await this.page.locator('button[name="allowanceType"]').click();

    // await this.page.getByText(/^Case$/).nth(2).click();
    // await page.goto('https://memsp-dev.albertsons.com/memsp-ui-shell/meupp/events/');
    // await page.locator('button[name="allowanceType"]').click();
    // await page.getByText('Case').click();
    await this.page.waitForLoadState("networkidle");
    // await this.page.getByRole('button', { name: 'Case' }).click();

    await this.page.locator('div').filter({ hasText: /^Case$/ }).nth(2).click();
    await this.page.waitForLoadState("networkidle");
    // Verify the "Performance*" dropdown is visible
    await expect(this.page.getByText('Performance*')).toBeVisible();

    // Verify that "DSD Off Invoice (01)" is auto-populated
    const allowanceTypeButton = this.page.getByRole('button', { name: 'DSD Off Invoice (01)' });
    await expect(allowanceTypeButton).toBeVisible();

    await expect(allowanceTypeButton).toHaveText('DSD Off Invoice (01)');


    await expect(this.page.getByText('One Allowance: DSD Combined')).toBeVisible();
    await this.page.locator('label').filter({ hasText: 'One Allowance: DSD Combined' }).getByTestId('radio-check').click();
    await this.page.waitForLoadState("networkidle");
    await expect(this.page.getByLabel('backdrop-dialog')).toBeVisible();
    await this.page.waitForLoadState("networkidle");
    await expect(this.page.getByText('Select DSD Separate to create')).toBeVisible();
    await this.page.waitForLoadState("networkidle");
    await expect(this.page.getByText('Allowance Dates')).toBeVisible();
    await this.page.waitForLoadState("networkidle");

    // Verify that the "Vehicle Type/Custom Date*" dropdown is visible and pre-populated with "Promo Cycle"
    const vehicleTypeDropdown = this.page.getByRole('button', { name: 'Vehicle Type/Custom Date' });
    await vehicleTypeDropdown.scrollIntoViewIfNeeded();

    await expect(vehicleTypeDropdown).toBeVisible();
    await expect(vehicleTypeDropdown).toHaveText('Promo Cycle');



    // Verify 
    const yearDropdown = this.page.locator('div:has-text("Year") >> div[role="button"]');
    // await yearDropdown.scrollIntoViewIfNeeded();
    await expect(yearDropdown).toBeVisible();

    const startWeekDropdown = this.page.locator('div:has-text("Start Week/Vehicle") >> div[role="button"]');
    // await startWeekDropdown.scrollIntoViewIfNeeded();
    await expect(startWeekDropdown).toBeVisible();
    // 


    await this.page.getByRole('button', { name: 'Edit Divison Dates' }).click();
    await this.page.waitForLoadState("networkidle");
    await expect(this.page.getByText('Applying Powerline will')).toBeVisible();

    await this.page.getByRole('button', { name: 'Review Overlaps & Enter' }).click();
    await this.page.waitForLoadState("networkidle");
    await expect(this.page.getByLabel('backdrop-dialog').locator('div').nth(1)).toBeVisible();

    // await this.page.goto(`${baseUrl}events/allowances-entry?eventId=682db17bf58c015230ada088&group=DSD_WHSE_RETAIL_DIVISION&isEdit=false&isNdpType=true`);
    // https://memsp-dev.albertsons.com/memsp-ui-shell/meupp/events/allowances-entry?eventId=682f715059529a66b114cfcd&group=DSD_WHSE_RETAIL_DIVISION&isEdit=false&isNdpType=true
    // Step 1: Wait for navigation to finish (edit page)

    await this.page.waitForURL('**/events/edit/**');

    // Step 2: Extract the current URL
    const currentUrl = this.page.url();

    // Step 3: Extract the eventId from the URL
    const eventIdMatch = currentUrl.match(/events\/edit\/([a-zA-Z0-9]+)/);

    if (!eventIdMatch || !eventIdMatch[1]) {
      throw new Error('Event ID not found in URL');
    }

    const eventId = eventIdMatch[1];

    // Step 4: Construct the new URL and navigate
    const newUrl = `${baseUrl}events/allowances-entry?eventId=${eventId}&group=DSD_WHSE_RETAIL_DIVISION&isEdit=false&isNdpType=true`;
    await this.page.goto(newUrl);


    await this.page.waitForLoadState("networkidle");

    await expect(this.page.getByText('Allowance Amounts|Case - DSD Combined')).toBeVisible();

    await expect(this.page.getByText('You can update Allowance')).toBeVisible();

    await expect(this.page.getByText('Allow Amounts:')).toBeVisible();
    await this.page.getByRole('spinbutton').click();
    await this.page.getByRole('spinbutton').fill('2');
    await this.page.getByRole('button', { name: 'Update All Divisions' }).click();
    await this.page.waitForLoadState("networkidle");
    await this.page.getByText('- Norcal').click();

    await this.page.getByRole('spinbutton').click();
    await this.page.getByRole('spinbutton').fill('4');
    await this.page.getByRole('button', { name: 'Update Selected Division Only' }).click();
    await this.page.waitForLoadState("networkidle");
    await expect(this.page.getByText('Case Amount')).toBeVisible();
    // await expect(this.page.getByRole('row', { name: 'DESES TTHPASTE WHTNNG TTREE' }).getByTestId('uom-text')).toBeVisible();
    // Verify that the input field for 'DESES TTHPASTE WHTNNG TTREE' is updated with the value '4'
    const row = this.page.getByRole('row', { name: 'DESES TTHPASTE WHTNNG TTREE' });
    const inputField = row.getByTestId('uom-text');

    // Ensure the input field is visible
    await expect(inputField).toBeVisible();

    // Fill the input field with the value '4'
    await inputField.fill('4');

    // Verify that the input field contains the value '4'
    await expect(inputField).toHaveValue('4');




    await this.page.getByRole('button', { name: 'Save All Changes' }).click();
    await this.page.waitForLoadState("networkidle");

    await this.page.goto('https://memsp-dev.albertsons.com/memsp-ui-shell/meupp/events/edit/682db17bf58c015230ada088');
    await this.page.waitForLoadState("networkidle");
    await expect(this.page.getByText('Review Overlaps & Allowance')).toBeVisible();
    await expect(this.page.getByText('Allowance Amount', { exact: true })).toBeVisible();
    await expect(this.page.getByText('$2.00 - $4.00 CA')).toBeVisible();
    await expect(this.page.locator('#abs-basic-allowance-amount-view-all-link')).toContainText('Edit / View All Items');
    await expect(this.page.getByText('Billing Details')).toBeVisible();
    await expect(this.page.getByText('For reference, the items in')).toBeVisible();
    await expect(this.page.getByText('Suggested A/P or A/R Number')).toBeVisible();
    await this.page.locator('input[name="acApOrArNumberAll"]').click();
    await this.page.locator('input[name="acApOrArNumberAll"]').fill('11106784');
    await expect(this.page.getByText('Suggested Payment Type')).toBeVisible();
    await this.page.getByRole('button', { name: 'Select' }).click();
    await this.page.getByTestId('popper').getByText('Invoice').click();
    await expect(this.page.getByText('Select Divisions to Update')).toBeVisible();
    await expect(this.page.getByText('Selected (11)')).toBeVisible();
    await this.page.getByRole('textbox', { name: 'Enter Vendor Comments' }).click();
    await this.page.getByRole('textbox', { name: 'Enter Vendor Comments' }).fill('Testing Offer creation flow with DSD');
    await this.page.getByRole('button', { name: 'Apply' }).click();
    await this.page.getByRole('button', { name: 'Edit Division Billing Info' }).click();
    await this.page.getByRole('button', { name: '- Denver' }).click();
    await expect(this.page.locator('input[name="allowanceBillingInfo\\.\\[0\\]\\.\\[0\\]\\.acApOrArNumber"]')).toBeVisible();
    await expect(this.page.locator('button[name="allowanceBillingInfo\\.\\[0\\]\\.\\[0\\]\\.suggestedVendorPaymentType"]')).toBeVisible();
    await expect(this.page.locator('input[name="allowanceBillingInfo\\.\\[0\\]\\.\\[0\\]\\.vendorComment"]')).toBeVisible();
    await this.page.getByRole('button', { name: 'Edit Division Billing Info' }).click();
    await expect(this.page.getByText('Vendor Tracking Number')).toBeVisible();
    await this.page.locator('input[name="vendorOfferTrackingNbr"]').click();
    await this.page.locator('input[name="vendorOfferTrackingNbr"]').fill('1528044');
    await this.page.getByRole('button', { name: 'Save & Create Allowance(s)' }).click();
    await expect(this.page.getByText('Offer # 7024800 - Case - DSD')).toBeVisible();
    await expect(this.page.getByTestId('tag-id')).toBeVisible();
    await this.page.getByTestId('tag-id').getByText('draft').click();
    await expect(this.page.locator('#abs-card-container-is-agreement-clicked-sec').getByText('draft')).toBeVisible();

    // // await this.page.getByText('Performance*').click();
    // // await this.page.getByRole('button', { name: 'DSD Off Invoice (01)' }).click();
    // // await this.page.getByRole('button', { name: 'DSD Off Invoice (01)' }).press('Escape');
    // // await expect(this.page.getByText('Vehicle Type/Custom Date')).toBeVisible();
    // // await expect(this.page.getByRole('button', {name: 'Vehicle Type/Custom Date'})).toBeVisible();
    // // await expect(globalThis.page.getByText('Year')).toBeVisible();


    // // await expect(globalThis.page.getByRole('button', { name: 'Vehicle Type/Custom Date*' })).toBeVisible();

    // // await globalThis.page.getByText('Vehicle Type/Custom Date*').click();

    // // await globalThis.page.getByText('Vehicle Type/Custom Date*').click();



    // // await page1.getByRole('button', { name: 'Vehicle Type/Custom Date' }).click();
    // // await page1.getByRole('button', { name: 'Vehicle Type/Custom Date' }).press('Escape');


    // await globalThis.page.getByText('Scan').click();
    // // await page.locator('div').filter({ hasText: /^Scan$/ }).nth(3).click();
    // // await page.locator('div').filter({ hasText: /^Scan$/ }).nth(2).click();


    // // await globalThis.page.getByTestId('popper').getByText('Case').click();

    // // Verify if it is warehuse or One allowance DSD Combined
    // const labelElement = globalThis.page.getByTestId('allowance-to-be-created-content-input').locator('label');
    // await expect(labelElement).toBeVisible();

    // const labelText = await labelElement.innerText();
    // console.log("labelText>", labelText);

    // if (labelText.includes('Warehouse')) {

    //   const radio = globalThis.page.getByTestId('radio-check');
    //   // Check if already selected
    //   const isChecked = await radio.isChecked();
    //   if (!isChecked) {
    //     await radio.click();
    //   }

    //   await expect(globalThis.page.getByText('Warehouse Allowance Dates')).toBeVisible();
    // } else if (labelText.includes('One Allowance: DSD Combined')) {
    //   await globalThis.page.getByText('One Allowance: DSD Combined').click();
    // } else {
    //   throw new Error(`Unexpected allowance type label: ${labelText}`);
    // }

    // await globalThis.page.getByRole('button', { name: 'Review Overlaps & Enter' }).click();
    // await globalThis.page.getByRole('spinbutton').fill('1');
    // await globalThis.page.getByRole('button', { name: 'Update All Divisions' }).click();

    // await globalThis.page.getByRole('button', { name: 'Save All Changes' }).click();
    // await globalThis.page.waitForURL('**/events/edit/**');
    // await globalThis.page.getByRole('button', { name: 'Select' }).click();
    // await globalThis.page.getByText('Invoice').click();

    // await globalThis.page.getByRole('textbox', { name: 'Enter Vendor Comments' }).fill('Testing flow');
    // await globalThis.page.getByRole('button', { name: 'Apply' }).click();
    // await globalThis.page.getByRole('button', { name: 'Edit Division Billing Info' }).click();
    // await globalThis.page.getByRole('button', { name: 'Expand All' }).click();
    // await globalThis.page.getByRole('button', { name: 'Collapsed All' }).click();

    // await globalThis.page.locator('input[name="vendorOfferTrackingNbr"]').fill('11002277546');
    // await globalThis.page.getByRole('button', { name: 'Save & Create Allowance(s)' }).click();

    // await globalThis.page.getByRole('button', { name: 'View Offer Deal Sheet' }).click();
    // await globalThis.page.getByTestId('close-icon-div-id').locator('path').click();

    // await globalThis.page.getByRole('button', { name: 'Actions' }).click();
    // await globalThis.page.getByRole('button', { name: 'Send to Vendor' }).click();
    // await expect(globalThis.page.locator('#event-header').getByText('pending with vendor')).toBeVisible();
  }


  // async offerCreation() {

  // await expect(page.getByText('New Offer')).toBeVisible();
  // await expect(page.getByText('Allowance Type')).toBeVisible();
  // await page.getByRole('button', { name: 'Scan' }).click();
  // await page.locator('div').filter({ hasText: /^Case$/ }).nth(2).click();
  // await expect(page.getByRole('button', { name: 'Off Invoice (01)' })).toBeVisible();
  // await expect(page.getByTestId('allowance-to-be-created-content-input').locator('div').nth(1)).toBeVisible();
  // await expect(page.getByText('Warehouse Only')).toBeVisible();
  // await expect(page.getByText('Vehicle Type/Custom Date')).toBeVisible();

  // await expect(page.getByText('Year')).toBeVisible();
  // await expect(globalThis.page.getByRole('button', { name: '2025', exact: true })).toBeVisible();

  // }


}
