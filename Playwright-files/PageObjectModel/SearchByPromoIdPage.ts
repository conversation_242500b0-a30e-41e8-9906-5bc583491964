import { Page } from '@playwright/test';

export class SearchByPromoIdPage {
  constructor(private page: Page) {}

  async applySearchFilters() {
    // Change Offer ID# to Promo ID#
    await this.page.getByRole('button', { name: 'Offer ID#' }).click();
    await this.page.getByText('Promo ID#').click();

    // Tasks -> Planning
    await this.page.getByRole('button', { name: 'Tasks' }).click();
    await this.page.getByText('Planning').click();

    // Open Filter
    await this.page.locator('div').filter({ hasText: /^Filter$/ }).nth(1).click();

    // Group and Categories
    await this.page.getByRole('button', { name: 'Group and Categories(99)' }).click();
    await this.page.locator('#abs-accordion-container-body-abs-facet-filter-container-2 label')
      .filter({ hasText: 'Select All' }).getByRole('img').click();
    await this.page.getByRole('button', { name: 'Group and Categories(1193)' }).click();

    // Category Owner
    await this.page.getByRole('button', { name: 'Category Owner' }).click();
    await this.page.locator('#abs-accordion-container-body-abs-facet-filter-container-3 label')
      .filter({ hasText: 'Select All' }).locator('div').nth(2).click();
    await this.page.getByRole('button', { name: 'Category Owner(141)' }).click();

    // Promo Type
    await this.page.getByRole('button', { name: 'Promo Type' }).click();
    await this.page.locator('#abs-filter-item-list-label-wrapper-BUY_ONE_GET_ONE-promoType-0 div').nth(2).click();
    await this.page.locator('#abs-filter-item-list-label-wrapper-BUY_X_GET_ONE-promoType-1 div').nth(2).click();
    await this.page.locator('#abs-filter-item-list-label-wrapper-CENT_OFF-promoType-2 div').nth(2).click();
    await this.page.locator('#abs-filter-item-list-label-wrapper-NET_PRICE-promoType-3 div').nth(2).click();
    await this.page.locator('#abs-filter-item-list-label-wrapper-PERCENT_OFF-promoType-4 div').nth(2).click();
    await this.page.getByRole('button', { name: 'Show(1)More' }).click();
    await this.page.locator('#abs-filter-item-list-label-wrapper-UN_SUPPORTED-promoType-5 div').nth(2).click();
    await this.page.getByRole('button', { name: 'Promo Type(6)' }).click();

    // Apply and close filters
    await this.page.getByTestId('apply-btn').click();
    await this.page.getByRole('img', { name: 'close-icon' }).click();
  }

  async searchByPromoId(promoId: string) {
    await this.page.locator('input[placeholder="Search"]').click();
    await this.page.locator('input[placeholder="Search"]').fill(promoId);
    await this.page.locator('div:nth-child(4) > .lucide').click();
    await this.page.getByTestId('link-by-product-d').click();
    await this.page.getByTestId('close-icon-div-id').getByRole('img').click();
    await this.page.getByRole('button', { name: 'BACK' }).click();
  }
}
