import { expect, Page } from "playwright/test";
import { DivisionSelectionComponent } from "../../src/ui/Components/baseComponent/EventFields/Division/DivisionSelectionComponent";
import { ProductGroupComponent } from "../../src/ui/Components/baseComponent/EventFields/ProductGroup/ProductGroupComponent";
import { VehicleComponent } from "../../src/ui/Components/baseComponent/EventFields/Vehicle/VehicleComponent";
import { StoreGroupComponent } from "../../src/ui/Components/baseComponent/EventFields/StoreGroup/StoreGroupComponent";

export class DivisionAllowanceOnlyCreationPage {
    private divisionComponent: DivisionSelectionComponent;
    private productGroupComponent: ProductGroupComponent;
    private storeGroupComponent: StoreGroupComponent;
    private vehicleComponent: VehicleComponent;

    constructor(private page: Page) {
        this.divisionComponent = new DivisionSelectionComponent(page);
        this.productGroupComponent = new ProductGroupComponent(page);
        this.storeGroupComponent = new StoreGroupComponent(page);
        this.vehicleComponent = new VehicleComponent(page);
    }

    async divisionSelection(divisionData: any) {
        await this.divisionComponent.selectDivision(divisionData);
    }

    async populateProductGroupDivisionAllowanceOnly(ppgData: any) {
        await this.productGroupComponent.selectProductGroup(ppgData);
    }

    async ViewPPGStores(ViewPPGStoresData: any) {
        await this.productGroupComponent.viewPPGStores(ViewPPGStoresData);
        await this.page.waitForLoadState("networkidle");
    }

    async getStoreGroupType(storeGroupTypeData: any) {
        await this.storeGroupComponent.getStoreGroupType(storeGroupTypeData);
    }

    async getStoreGroups(storeGroupsData: any) {
        await this.storeGroupComponent.getStoreGroups(storeGroupsData);
    }

    async vehicleTypeOrCustomDate(option: 'Other' | 'Promo Cycle' | 'Custom Date') {
        await this.vehicleComponent.selectVehicleType(option);
    }

    async yearSelection(yearData: any) {
        await this.vehicleComponent.selectYear(yearData);
    }

    async startWeekOrVehicle(week: string) {
        await this.vehicleComponent.selectStartWeek(week);
    }

    async prepopulateVehicleStartDate(date: string) {
        await this.vehicleComponent.verifyStartDate(date);
    }

    async prepopulateVehicleEndDate(date: string) {
        await this.vehicleComponent.verifyEndDate(date);
    }

    async saveEventDetails() {
        await this.page.getByRole('button', { name: /Save Event Details & Add/ }).click();
    }
}