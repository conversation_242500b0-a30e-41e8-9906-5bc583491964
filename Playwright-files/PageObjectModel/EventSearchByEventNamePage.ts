import { Page } from '@playwright/test';

export class EventSearchByEventNamePage {
  readonly page: Page;

  constructor(page: Page) {
    this.page = page;
  }

  async selectEventNameSearch() {
    await this.page.getByRole('button', { name: 'Offer ID#' }).click();
    await this.page.getByText('Event Name').click();
  }

  async filterByPlanningTask() {
    await this.page.getByRole('button', { name: 'Tasks' }).click();
    await this.page.getByText('Planning').click();
  }

  async searchEventByName(eventName: string) {
    const searchBox = this.page.getByRole('textbox', { name: 'Search' });

    await searchBox.fill(eventName);
    await this.page.locator('div:nth-child(4) > .lucide').click();

    // Click the first matching link with event name (partial match to avoid strict error)
    await this.page.getByRole('link', { name: new RegExp(eventName.split(' ')[0], 'i') }).first().click();
  }
}
