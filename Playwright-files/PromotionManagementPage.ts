import { expect, Page } from '@playwright/test';

export class PromotionManagementPage {
  constructor(private page: Page) {}

  async navigateToPromotionManagement() {
    await this.page.goto('https://memsp-qa2.albertsons.com/memsp-ui-shell/meupp/');
  }

  async verifyPromotionManagementHeading() {
    await expect(this.page.getByRole('heading', { name: 'Promotion Management' })).toBeVisible();
  }

  async clickAddEvent() {
    await this.page.getByRole('link', { name: 'Add Event' }).click();
  }

  async verifyNewEventVisible() {
    await expect(this.page.getByText('New Event')).toBeVisible();
  }

  async selectNationalEvent() {
    await expect(this.page.getByText('National', { exact: true })).toBeVisible();
    await this.page.getByRole('button', { name: 'Get Started' }).nth(2).click();
  }

  async searchPromoProductGroup() {
    await this.page.getByRole('textbox', { name: 'Search Promo Product Groups' }).click();
    await this.page.getByText('749293 - ACT Dry Mouth CIG').click();
  }

  async selectPromoCycle() {
    await this.page.getByRole('button', { name: 'Vehicle Type/Custom Date' }).click();
    await this.page.getByText('Promo Cycle').click();
    await this.page.getByRole('button', { name: 'Start Week/Vehicle' }).click();
    await this.page.getByText('GMHBC P05 2025 - 06/18/25 - 07/15/').click();
  }

  async saveEventDetails() {
    await this.page.getByRole('button', { name: 'Save Event Details & Add' }).click();
  }

  async handleWarningPopup() {
    await expect(this.page.getByText('Warning:')).toBeVisible();
    await expect(this.page.getByText('The following divisions do')).toBeVisible();
    await expect(this.page.getByText('- United')).toBeVisible();
    await expect(this.page.getByText('Do you want to proceed with')).toBeVisible();
    await this.page.getByRole('button', { name: 'OK' }).click();
  }

  async verifyNewOfferVisible() {
    await expect(this.page.getByText('New Offer')).toBeVisible();
  }

  async fillAllowanceDetails() {
    await this.page.locator('button[name="allowanceType"]').click();
    await this.page.locator('div').filter({ hasText: /^Case$/ }).nth(2).click();
    await this.page.getByRole('button', { name: 'Review Overlaps & Enter' }).click();
    await expect(this.page.getByText('Allowance amount changes for')).toBeVisible();
    await this.page.getByRole('button', { name: 'OK' }).click();
  }

  async updateAllowanceAmount() {
    await this.page.getByRole('spinbutton').click();
    await this.page.getByRole('spinbutton').fill('2');
    await this.page.getByRole('button', { name: 'Update All Divisions' }).click();
    await this.page.getByRole('button', { name: 'Save All Changes' }).click();
  }
}