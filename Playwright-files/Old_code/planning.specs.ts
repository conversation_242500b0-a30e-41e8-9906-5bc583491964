import { test, expect, chromium } from "@playwright/test";
import { getConfig } from "../../src/Utils/config-utils";
import {
  regenerateAuthState,
  clearAuthState,
  isAuthStateValid,
} from "../../src/Utils/auth-utils"; // Adjust the path as needed
import { MongoClient } from "mongodb"; // Import MongoDB client

const env = process.env.TEST_ENV || "dev"; // Default to "dev" if TEST_ENV is not set
const { baseUrl, authStateFile } = getConfig(env);

test.describe("Planning Tests", () => {
  let browser, context, page;

  test.beforeEach(async () => {
    browser = await chromium.launch({ headless: true, slowMo: 500 });
    context = await browser.newContext({ storageState: authStateFile });
    page = await context.newPage();
    // await regenerateAuthState(page, context);
    await page.goto(baseUrl);
    await page.waitForLoadState("networkidle");

    // Handle permission errors and validate auth state
    if (await page.locator("text=You do not have permission").isVisible()) {
      console.log("Permission error detected. Regenerating auth.json...");
      await clearAuthState();
      await regenerateAuthState(page, context);
      await page.goto(baseUrl);
      await page.waitForLoadState("networkidle");
    }

    if (!(await isAuthStateValid({ page }))) {
      console.log("auth.json is invalid. Regenerating...");
      await regenerateAuthState(page, context);
      await page.goto(baseUrl);
      await page.waitForLoadState("networkidle");
    }
  });

  test.afterEach(async () => {
    await page.close();
    await context.close();
    await browser.close();
  });

  test("Planning Page", async () => {
    await page.getByRole("button", { name: "Tasks" }).click();
    await page.getByText("Planning").click();
    await page.getByRole("button", { name: "Offer ID#" }).click();
    await page.getByText("Event ID#").click();
    await page.getByRole("textbox", { name: "Search" }).fill("10050753");
    await page.locator("div:nth-child(4) > .lucide").click();

    await expect(page.getByRole("button", { name: "Planning" })).toBeVisible();
  });

  test("Planning Page with API Validation", async () => {
    const capturedResponses = [];

    // Listen for network responses
    page.on("response", async response => {
      if (
        response.url().includes("/plan/task/get/product-groups") &&
        response.request().postData()?.includes("10050753")
      ) {
        const responseBody = await response.json();
        capturedResponses.push({
          url: response.url(),
          status: response.status(),
          body: responseBody,
        });
      }
    });

    // Perform the search action
    await page.getByRole("button", { name: "Tasks" }).click();
    await page.getByText("Planning").click();
    await page.getByRole("button", { name: "Offer ID#" }).click();
    await page.getByText("Event ID#").click();
    await page.getByRole("textbox", { name: "Search" }).fill("10050753");

    // Trigger the search by clicking the search icon
    await page.locator("div:nth-child(4) > .lucide").click();

    // Wait for the response
    //     const productGroupsResponse = await page.waitForResponse(
    //       response =>
    //         response.url().includes("/plan/task/get/product-groups") &&
    //         response.request().postData()?.includes("10050753")
    // );

    // Validate the captured response
    const productGroupsResponse = capturedResponses.find(res =>
      res.url.includes("/plan/task/get/product-groups")
    );
    expect(productGroupsResponse).toBeDefined();
    expect(productGroupsResponse.status).toBe(200); // Ensure the response status is 200

    // Validate the response body
    const responseBody = productGroupsResponse.body;
    expect(responseBody.eventTaskProductGroups).toBeDefined();
    expect(responseBody.eventTaskProductGroups[0].planEventIdNbr).toBe(
      10050753
    );
    console.log("Product Groups Response Body:", responseBody);

    // Additional assertions can be added here to validate specific fields in the response
    await expect(page.getByRole("button", { name: "Planning" })).toBeVisible();
  });

  test("Planning Page with API and MongoDB Validation", async () => {
    const capturedResponses = [];
    const mongoUri =
      "mongodb+srv://meupp-dev-appuser:<EMAIL>/meupp-dev"; // Replace with your MongoDB connection string
    const mongoClient = new MongoClient(mongoUri);

    // Listen for network responses
    page.on("response", async response => {
      if (
        response.url().includes("/plan/task/get/product-groups") &&
        response.request().postData()?.includes("10050753")
      ) {
        const responseBody = await response.json();
        capturedResponses.push({
          url: response.url(),
          status: response.status(),
          body: responseBody,
        });
      }
    });

    // Perform the search action
    await page.getByRole("button", { name: "Tasks" }).click();
    await page.getByText("Planning").click();
    await page.getByRole("button", { name: "Offer ID#" }).click();
    await page.getByText("Event ID#").click();
    await page.getByRole("textbox", { name: "Search" }).fill("10050753");

    // Trigger the search by clicking the search icon
    await page.locator("div:nth-child(4) > .lucide").click();

    // Wait for a short time to ensure the response is captured
    await page.waitForTimeout(2000);

    // Validate the captured response
    const productGroupsResponse = capturedResponses.find(res =>
      res.url.includes("/plan/task/get/product-groups")
    );
    expect(productGroupsResponse).toBeDefined();
    expect(productGroupsResponse.status).toBe(200); // Ensure the response status is 200

    // Validate the response body
    const responseBody = productGroupsResponse.body;
    expect(responseBody.eventTaskProductGroups).toBeDefined();
    expect(responseBody.eventTaskProductGroups[0].planEventIdNbr).toBe(
      10050753
    );

    // Connect to MongoDB and validate data
    try {
      await mongoClient.connect();
      const db = mongoClient.db("meupp-dev"); // Replace with your database name
      const collection = db.collection("planevents"); // Replace with your collection name

      // Query the database for the same Event ID
      const dbData = await collection.findOne({ planEventIdNbr: 10050753 });
      expect(dbData).toBeDefined();
      expect(dbData?.planEventIdNbr).toBe(10050753);

      // Compare API response with database data
      expect(responseBody.eventTaskProductGroups[0].eventName).toBe(
        dbData?.eventName
      );
      console.log("Mongo Database Data:", dbData);
    } finally {
      await mongoClient.close(); // Ensure the MongoDB connection is closed
    }

    console.log("Product Groups Response Body:", responseBody);

    // Additional assertions can be added here to validate specific fields in the response
    await expect(page.getByRole("button", { name: "Planning" })).toBeVisible();
  });
});
