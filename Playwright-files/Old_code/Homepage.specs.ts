import { test, expect, chromium } from "@playwright/test";
import {
  regenerateAuthState,
  clearAuthState,
  isAuthStateValid,
} from "../../src/Utils/auth-utils"; // Adjust the path as needed
import { getConfig } from "../../src/Utils/config-utils"; // Adjust the path as needed

const env = process.env.TEST_ENV || "dev"; // Default to "dev" if TEST_ENV is not set
const { baseUrl, authStateFile, apimPath, domain } = getConfig(env);
// const fullTaskAlertEndpoint = `${apimPath}${taskAlertEndpoint}`;

let browser; // Shared browser instance
let context; // Shared browser context
let page; // Shared page instance
let capturedRequests: {
  url: string;
  method: string;
  postData: string | null;
}[] = []; // Array to store captured requests
let capturedResponses: { url: string; status: number; body: string }[] = []; // Array to store captured responses

test.beforeAll(async () => {
  //await clearAuthState();

  // Launch the browser and set up the context and page
  browser = await chromium.launch({ headless: true, slowMo: 500 }); // Open browser for debugging
  context = await browser.newContext({ storageState: authStateFile });
  page = await context.newPage();

  //await page.goto("https://apex-dev.albertsons.com/apex/main#!Worklist");
  // await regenerateAuthState(page, context);

  // await page.goto(
  //   "https://login.microsoftonline.com/b7f604a0-00a9-4188-9248-42f3a5aac2e9/oauth2/v2.0/authorize?client_id=3b48c2b7-5a4e-48f0-8e7d-999c9b9003d2&scope=openid%20profile%20offline_access&redirect_uri=https%3A%2F%2Fmemsp-dev.albertsons.com%2Fmemsp-ui-shell%2FazureAdRedirect&client-request-id=d3391317-1faf-431a-8d09-704fcef406e1&response_mode=form_post&response_type=code&x-client-SKU=msal.js.node&x-client-VER=1.18.2&x-client-OS=linux&x-client-CPU=x64&client_info=1&state=eyJjc3JmVG9rZW4iOiJiNmNjNzBiZC04ZTM2LTRjMGMtOGRmYi0wZjc1ZmI0ZGY2YjMiLCJyZWRpcmVjdFRvIjoiL21lbXNwLXVpLXNoZWxsL21ldXBwLyJ9&sso_reload=true"
  // );

  // Capture network requests
  context.on("request", request => {
    if (request.url().includes("/abs/devint/meupp")) {
      // console.log(`Captured Request: ${request.method()} ${request.url()}`);
      capturedRequests.push({
        url: request.url(),
        method: request.method(),
        postData: request.postData(),
      });
    }
  });

  // Capture network responses
  context.on("response", async response => {
    if (response.url().includes("/abs/devint/meupp")) {
      // console.log(`Captured Response: ${response.status()} ${response.url()}`);
      const responseBody = await response.text(); // Capture the response body
      capturedResponses.push({
        url: response.url(),
        status: response.status(),
        body: responseBody,
      });
    }
  });

  // Navigate to the homepage
  await page.goto(baseUrl);
  await page.waitForLoadState("networkidle"); // Ensure the page is fully loaded

  // Check if the modal with the permission error is visible
  const permissionErrorVisible = await page
    .locator(
      "text=You do not have permission to access the Albertsons Partner Portal"
    )
    .isVisible();

  if (permissionErrorVisible) {
    console.log("Permission error detected. Regenerating auth.json...");
    await clearAuthState();
    await regenerateAuthState(page, context);
    // Reload the page after regenerating auth state
    await page.goto(baseUrl);
    await page.waitForLoadState("networkidle");
  }

  // Additional validation for auth state
  if (!(await isAuthStateValid({ page }))) {
    console.log("auth.json is invalid. Regenerating...");
    await regenerateAuthState(page, context);
    await page.goto(baseUrl);
    await page.waitForLoadState("networkidle");
  }
});

test.afterAll(async () => {
  // Close the shared page and browser after all tests
  await page.close();
  await context.close();
  await browser.close();
});

test.describe("Filter Tests", () => {
  test("should capture all API requests on the page", async ({ page }) => {
    // Log all captured requests
    console.log("Captured Requests:", capturedRequests);

    // Log all captured responses
    console.log("Captured Responses:", capturedResponses);

    // Optionally, add assertions to validate specific requests and responses
    const performanceRequest = capturedRequests.find(req =>
      req.url.includes(`${apimPath}/plan/event/task-alert`)
    );
    expect(performanceRequest).toBeDefined();

    const performanceResponse = capturedResponses.find(res =>
      res.url.includes(`${apimPath}/plan/event/task-alert`)
    );
    console.log("Performance Response:", performanceResponse);
    // expect(performanceResponse).toBeDefined();
    // expect(performanceResponse?.status).toBe(200); // Validate response status if defined
    // expect(performanceResponse?.body).toContain("expectedData"); // Validate response body
  });

  test("should display the promotion management page", async () => {
    //await page.pause();
    const title = page.locator(".abs-pm-promotion-head__heading");
    title.dblclick();
    await expect(title).toHaveText("Promotion Management");
  });

  test("should allow filtering by division", async () => {
    //await page.pause();
    await page.getByRole("img", { name: "filter" }).click();
    await page.getByRole("button", { name: "Division(1)" }).click();
    await expect(
      page.getByRole("button", { name: "Division(1)" })
    ).toBeVisible();
  });
  test("should allow filtering by event type", async () => {
    //await page.pause();
    // Click the "Event Type" filter
    // await page.getByRole("img", { name: "filter" }).click();
    await page.getByRole("button", { name: "Event Type" }).click();
    await expect(
      page.getByRole("button", { name: "Event Type" })
    ).toBeVisible();
    // Verify the filter options are visible
    await expect(
      page.getByRole("button", { name: "Event Type" })
    ).toBeVisible();

    await page
      .locator("#abs-filter-item-list-label-wrapper-DP-eventType-0 div")
      .nth(2)
      .click();
    await expect(page.getByText("Division Promotion")).toBeVisible();
    await expect(page.getByText("Division Allowance Only")).toBeVisible();
    //scroll down to performance
    await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
    // Wait for new content to load (change this value as needed)
    await page.waitForTimeout(1000); // wait for 1000 milliseconds
    await page.getByRole("button", { name: "Performance" }).click();
    await page.getByRole("button", { name: "Show(18)More" }).click();
  });

  test("should close the filter menu", async () => {
    await page.getByRole("heading", { name: "Sort & Filter" }).click();
    await page.getByRole("img", { name: "close-icon" }).locator("path").click();
    await expect(
      page
        .locator("div")
        .filter({ hasText: /^Filter$/ })
        .nth(1)
    ).toBeVisible();
  });
});

test.describe("Create new Event", async () => {
  test("should navigate to Add Event and validate options", async () => {
    // Verify the "Add Event" link is visible
    await expect(page.getByRole("link", { name: "Add Event" })).toBeVisible();

    // Click the "Add Event" link
    await page.getByRole("link", { name: "Add Event" }).click();

    // Verify the presence of event options
    await expect(
      page.getByText("Division Promotion", { exact: true })
    ).toBeVisible();
    await expect(page.getByText("Division Allowance Only")).toBeVisible();

    // Click the "Get Started" button
    await page.getByRole("button", { name: "Get Started" }).first().click();

    // Verify the "New Event" page is displayed
    await expect(page.getByText("New Event")).toBeVisible();
  });

  test("should display the new event and create the event", async () => {
    // Verify the new event is displayed in the list
    await expect(page.getByText("New Event")).toBeVisible();

    // Helper function to verify visibility of multiple elements
    const verifyElementsVisible = async (elements: string[]) => {
      for (const element of elements) {
        await expect(page.getByText(element, { exact: true })).toBeVisible();
      }
    };

    // Verify key elements on the page
    await verifyElementsVisible([
      "Division Promotion",
      "Event Details",
      "Division*",
      "Store Group Type*",
      "Store Groups*Seattle All Stores (221)View Stores",
      "Vehicle Type/Custom Date*",
      "Start Week/Vehicle*",
      "Vehicle Start*",
      "Vehicle End*",
      "Event Name*",
    ]);

    // Select a division
    await page.getByRole("button", { name: "Division" }).click();
    await page
      .locator("div")
      .filter({ hasText: /^27-SEATTLE$/ })
      .nth(3)
      .click();
    await expect(page.getByRole("button", { name: "Division" })).toBeVisible();

    // Verify Promo Product Groups section
    await page
      .locator("div")
      .filter({ hasText: /^Promo Product Groups\*Or Enter CIC IDs$/ })
      .locator("#abs-input-auto-complete")
      .click();
    await page
      .locator("div")
      .filter({
        hasText: /^184779 - Annies Homegrown Snacks Box - 84882 - S - 12$/,
      })
      .nth(2)
      .click();
    await page.locator("#abs-input-auto-complete").first().click();

    await expect(page.getByText("Annies Homegrown Snacks Box -")).toBeVisible();

    // Select Vehicle Type and Start Week
    await page
      .getByRole("button", { name: "Vehicle Type/Custom Date" })
      .click();
    await page.getByText("Weekly Insert").click();
    await page.getByRole("button", { name: "Start Week/Vehicle" }).click();
    await page
      .locator("div")
      .filter({ hasText: /^27 Week 24 Insert 2025 - 06\/11\/25 - 06\/17\/25$/ })
      .nth(1)
      .click();

    // Verify additional elements
    await expect(
      page.locator("#abs-input-date-picker-uds-error > .relative").first()
    ).toBeVisible();
    await expect(page.locator('input[name="name"]')).toBeVisible();

    await expect(
      page.getByRole("button", { name: "Save Event Details & Add" })
    ).toBeVisible();

    await page
      .getByRole("button", { name: "Save Event Details & Add" })
      .click();

    // Find the API response for the event creation in capturedResponses with an exact match
    const eventCreationResponse = capturedResponses.find(
      res => res.url === `${domain}${apimPath}/plan/event` && res.status === 200
      // "https://memsp-dev.albertsons.com/abs/devint/meupp/plan/event"
      // `${domain}${apimPath}/plan/event`&&
    );

    if (!eventCreationResponse) {
      throw new Error("Event creation API response not found.");
    }

    const responseBody = JSON.parse(eventCreationResponse.body);
    console.log("Event Creation Response:", responseBody);

    // Verify the Event ID is displayed in the UI
    const eventIdText = await page.getByText(/EventID #\d+/).textContent();
    const eventIdFromUI = eventIdText?.match(/\d+/)?.[0];
    expect(eventIdFromUI).toBeDefined();

    // Compare the Event ID from the API response with the UI as strings
    expect(responseBody.planEventIdNbr.toString()).toBe(eventIdFromUI);

    await expect(page.getByText(responseBody.name)).toBeVisible();
    // Extract Promo Product Groups from the UI
    const promoProductGroupsFromUI = await page
      .locator("table >> text=Promo Product Groups")
      .locator("..")
      .locator("td")
      .nth(1)
      .textContent();

    console.log("Promo Product Groups from UI:", promoProductGroupsFromUI);

    // Extract and validate Promo Product Groups from the API response
    const promoProductGroupsFromAPI = responseBody.planProductGroups
      .map(
        (group: {
          sourceProductGroupId: number;
          name: string;
          displayInd: boolean;
          itemCount: Number;
        }) =>
          `${group.sourceProductGroupId.toString()} - ${group.name} - ${
            group.displayInd ? "M" : "S"
          } - ${group.itemCount.toString()}`
      )
      .join(", ");

    console.log("Promo Product Groups from API:", promoProductGroupsFromAPI);

    expect(promoProductGroupsFromUI?.trim()).toBe(promoProductGroupsFromAPI);

    await expect(page.getByText("EventID #")).toBeVisible();
    await expect(page.locator("#abs-event-creation-container"))
      .toMatchAriaSnapshot(`
      - button "BACK":
        - img
      - text: "/Event ID #\\\\d+/"
      - separator
      - text: Division Promotion
      - tooltip:
        - img
      - separator
      - img "event-status-icon"
      - text: draft
      - button "Actions":
        - img
      - img
      - text: Comments
      - paragraph: View History
      - img
      - img
      - text: Event Details
      - separator
      - text: Updated by PPAP MEQ03AS less than a minute ago
      - img
      - text: Edit Event Details
      - paragraph: /Annies Homegrown Snacks Box - \\d+ - \\d+ Week \\d+ Insert \\d+/
      - img
      - paragraph: Allowance
      - img
      - paragraph: Promo
      - table:
        - rowgroup:
          - row /Promo Product Groups \\d+ - Annies Homegrown Snacks Box - \\d+ - S - \\d+/:
            - cell "Promo Product Groups"
            - cell /\\d+ - Annies Homegrown Snacks Box - \\d+ - S - \\d+/
          - row /Store Groups Seattle All Stores \\(\\d+ Stores\\)/:
            - cell "Store Groups"
            - cell /Seattle All Stores \\(\\d+ Stores\\)/
          - row /Time Period \\d+\\/\\d+\\/\\d+ - \\d+\\/\\d+\\/\\d+ \\d+ Week \\d+ Insert \\d+/:
            - cell "Time Period"
            - cell /\\d+\\/\\d+\\/\\d+ - \\d+\\/\\d+\\/\\d+ \\d+ Week \\d+ Insert \\d+/:
              - separator
          - row "Vendor KEHE DISTRIBUTORS":
            - cell "Vendor"
            - cell "KEHE DISTRIBUTORS"
          - row "Promo Type Unavailable":
            - cell "Promo Type"
            - cell "Unavailable"
          - row "Regular Price -":
            - cell "Regular Price"
            - cell "-"
      - img
      - text: Items
      - img
      - img
      - text: New Offer Cancel Allowance Details
      - paragraph: Allowance Type
      - paragraph: "*"
      - button "Select":
        - img
      - paragraph: Performance
      - paragraph: "*"
      - button "Select" [disabled]:
        - img
      - text: Review Overlaps & Allowance Amounts Billing Details
      - button "Bypass Allowance"
      - tooltip:
        - img
      - img
      - img
      - text: Promotion
      - separator
      - text: Unavailable
      `);
  });

  ///////////

  test("should create New Offer", async () => {
    // await page.goto(
    //   `https://memsp-dev.albertsons.com/memsp-ui-shell/meupp/events/edit/${responseBody.planEventIdNbr.toString()}`
    // );
    await expect(page.locator("#abs-event-creation-layout-cont")).toContainText(
      "New Offer"
    );
    await expect(
      page.locator(
        "div:nth-child(2) > .transition-opacity > .transition-height > div > div"
      )
    ).toBeVisible();
    await page.locator('button[name="allowanceType"]').click();
    await page
      .locator("div")
      .filter({ hasText: /^Scan$/ })
      .nth(2)
      .click();
    await page.getByRole("button", { name: "Select" }).click();
    await page.getByText("Price / Ad / Display (88)").click();
    await page
      .locator("label")
      .filter({ hasText: "One Allowance: Warehouse, DSD" })
      .getByTestId("radio-check")
      .click();
    await expect(page.getByText("Allowance Dates")).toBeVisible();
    await page.getByRole("button", { name: "Review Overlaps & Enter" }).click();
    await expect(page.getByText("Review Overlaps & Allowance")).toBeVisible();
    await page.getByRole("button", { name: "Edit / View All Items" }).click();
    await page.getByRole("spinbutton").click();
    await page.getByRole("spinbutton").fill("0.01");
    await page.getByRole("button", { name: "Update All" }).click();
    await page.getByRole("button", { name: "Save All Changes" }).click();
    await expect(page.getByText("Billing Details")).toBeVisible();
    await page
      .getByRole("button", { name: "Save & Create Allowance(s)" })
      .click();

    await expect(
      page.locator("#abs-card-container-is-agreement-clicked-sec")
    ).toBeVisible();

    await expect(page.getByTestId("tag-id").getByText("draft")).toBeVisible();
  });
  test("should send the event/offer to vendor", async () => {
    await expect(page.getByRole("button", { name: "Actions" })).toBeVisible();
    await expect(
      page.getByRole("button", { name: "Send to Vendor" })
    ).toBeVisible();
    await expect(
      page.getByRole("button", { name: "Delete Draft" })
    ).toBeVisible();
    await page.getByRole("button", { name: "Send to Vendor" }).click();
    await expect(page.getByText("Are you sure you want to send")).toBeVisible();
    await expect(
      page.getByRole("button", { name: "Add Promotion" })
    ).toBeVisible();
    await expect(
      page
        .getByTestId("modal-id")
        .getByRole("button", { name: "Send to Vendor" })
    ).toBeVisible();
    await page
      .getByTestId("modal-id")
      .getByRole("button", { name: "Send to Vendor" })
      .click();

    await expect(
      page.locator("#event-header").getByText("pending with vendor")
    ).toBeVisible();

    await expect(
      page.getByTestId("tag-id").getByText("pending with vendor")
    ).toBeVisible();
  });
});
