import type { Locator, <PERSON> } from "@playwright/test";

export class SortAndFilterSectionPage {
  readonly sortAndFilterIconLocator: Locator;
  readonly divisionFilterExpandIconLocator: Locator;
  readonly eventTypeFilterExpandIconLocator: Locator;
  readonly nationalEventTypeCheckboxLocator: Locator;
  readonly applyButtonLocator: Locator;
  readonly groupAndCategoriesExpandIconLocator: Locator;
  readonly groupAndCategoriesSelectAllCheckboxLocator: Locator;
  readonly closeIconLocator: Locator;
  readonly vehicleTypeLocatorInSortAndFilter: Locator;
  readonly allowanceTypeButtonLocator: Locator;
  readonly performanceTypeButtonLocator: Locator;
  readonly promoTypeButtonLocator: Locator;
  readonly vehicleTypeCheckboxinFilter: Locator;
  readonly eventTypeCheckboxinFilter: Locator;

  constructor(private page: Page) {
    this.sortAndFilterIconLocator = this.page.locator(
      '//div[@id="abs-promotionManagementContainer"]/div[2]/div[2]/div/label'
    );
    this.divisionFilterExpandIconLocator = this.page.getByRole("button", {
      name: "Division(1)",
    });
    this.eventTypeFilterExpandIconLocator = this.page.getByRole("button", {
      name: /^Event Type/,
    });
    this.nationalEventTypeCheckboxLocator = this.page
      .locator(`#abs-filter-item-list-label-wrapper-NDP-eventType-2 div`)
      .nth(2);
    this.applyButtonLocator = this.page.getByTestId("apply-btn");
    this.groupAndCategoriesExpandIconLocator = this.page.locator(
      '//div[@id="abs-facet-filter-container-2"]/button/span'
    );
    this.groupAndCategoriesSelectAllCheckboxLocator = this.page
      .locator(
        "#abs-accordion-container-body-abs-facet-filter-container-2 label"
      )
      .filter({ hasText: "Select All" })
      .getByRole("img");
    this.closeIconLocator = this.page
      .getByRole("img", { name: "close-icon" })
      .locator("path");

    this.vehicleTypeLocatorInSortAndFilter =  this.page.locator(
      '//div[@id="abs-facet-filter-container-9"]/button/span'
    );
    this.allowanceTypeButtonLocator = this.page.getByRole("button", {
      name: "Allowance Type",
    });
    this.performanceTypeButtonLocator = this.page.getByRole("button", {
      name: "Performance",
    });
    this.promoTypeButtonLocator = this.page.getByRole("button", {
      name: "Promo Type",
    });
    this.vehicleTypeCheckboxinFilter = this.page.locator('#filter-select-all-dashboard-view-vehicleTypes + div');
    this.eventTypeCheckboxinFilter = this.page.locator('#filter-select-all-dashboard-view-eventTypes + div');

  }

  async clickSortAndFilterIcon() {
    await this.sortAndFilterIconLocator.click();
  }

  async expandDivisionFilter() {
    await this.divisionFilterExpandIconLocator.click();
  }

  async selectDivision(divisionNumber: string) {
    await this.page
      .locator(
        `#abs-filter-item-list-label-wrapper-${divisionNumber}-division-0 div`
      )
      .nth(2)
      .click();
  }

  async expandEventTypeFilter() {
    const eventTypeButton = this.eventTypeFilterExpandIconLocator;
    await eventTypeButton.waitFor({ timeout: 10000 });
    await eventTypeButton.click();
  }

  async selectEventType(eventType: string) {
    await this.page.waitForLoadState("networkidle");
    await this.nationalEventTypeCheckboxLocator.click();
    await this.page.waitForTimeout(4000);
  }

  async clickApplyButton() {
    await this.applyButtonLocator.click();
  }

  async expandGroupAndCategoriesFilter() {
    await this.groupAndCategoriesExpandIconLocator.click();
  }

  async selectAllGroupAndCategoriesCheckboxes() {
    const section = this.page
      .locator("p", { hasText: "Group and Categories" })
      .locator("..")
      .locator("..")
      .locator("..");
    const selectAllInput = section.locator(
      'input[data-testid="filter-select-all"]'
    );
    const isChecked = await selectAllInput.evaluate(
      (el: HTMLInputElement) => el.checked
    );
    const isPartiallySelected = await section
      .locator("svg.lucide-minus")
      .isVisible();

    if (!isChecked || isPartiallySelected) {
      await section.locator('label:has-text("Select All")').click();
    }
  }

  async selectAllVehicleTypeCheckboxes() {
    await this.vehicleTypeCheckboxinFilter.click();
  }

  async clickCloseIcon() {
    await this.page.waitForTimeout(2000);
    await this.closeIconLocator.click();
  }

  async openVehicleTypeInSortAndFilter() {
    await this.vehicleTypeLocatorInSortAndFilter.click();
  }

  async selectingVehicleTypeInSortAndFilter(vehicleType: string) {
    await this.page
      .getByText(vehicleType)
      .waitFor({ state: "visible", timeout: 5000 });
    await this.page.getByText(vehicleType).click();
  }

  async openAllowanceTypeInSortAndFilter() {
    await this.allowanceTypeButtonLocator.click();
  }

  async selectingAllowanceTypeInSortAndFilter(allowanceType: string) {
    await this.page
      .getByText(allowanceType)
      .waitFor({ state: "visible", timeout: 5000 });
    await this.page.getByText(allowanceType).click();
  }

  async openPerformanceInSortAndFilter() {
    await this.performanceTypeButtonLocator.click();
  }

  async selectingPerformanceInSortAndFilter(performance: string) {
    await this.page
      .getByText(performance)
      .waitFor({ state: "visible", timeout: 5000 });
    await this.page.getByText(performance).click();
  }

  async openPromoTypeInSortAndFilter() {
    await this.promoTypeButtonLocator.click();
  }

  async selectingPromoTypeInSortAndFilter(promoType: string) {
    await this.page
      .getByText(promoType)
      .waitFor({ state: "visible", timeout: 5000 });
    await this.page.getByText(promoType).click();
  }

  async selectAllEventTypeCheckboxesInSortAndFilter() {
    await this.eventTypeCheckboxinFilter.click();
    }
}
