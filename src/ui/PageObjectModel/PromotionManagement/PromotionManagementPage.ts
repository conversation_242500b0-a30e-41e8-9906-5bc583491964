import { expect, Page, Locator } from "@playwright/test";
import { EventManagementPage } from "../../../../src/ui/PageObjectModel/EventManagement/EventManagementPage";
import { pageFixture } from "../../../../config/pageFixtures";
import { PromotionManagementPageEnum } from "../../../../src/ui/Enums/PromotionManagementPageEnum";

const DEFAULT_PREF_NAME = "Playwright Test Pref";

export class PromotionManagementPage {
  readonly page: Page;
  readonly viewDropdown: Locator;
  readonly viewDropdownPlanningOption: Locator;
  readonly viewDropdownAlertsOption: Locator;
  readonly defaultSelectedSearchCriteriaOptionDropdownLocator: Locator;
  readonly searchTextboxLocator: Locator;
  readonly statusDropdownToggleOptionInPlanningViewLocator: Locator;
  readonly statusDraftOptionInPlanningViewLocator: Locator;
  readonly statusAgreedOptionInPlanningViewLocator: Locator;
  readonly searchIconLocator: Locator;
  readonly eventRecordLocator: Locator;
  readonly savedPreferencesDropdownLocator: Locator;
  readonly testPreferenceLocator: Locator;
  readonly preferenceSettingsMenuLocator: Locator;
  readonly updatePreferenceOptionLocator: Locator;
  readonly saveAsNewPreferenceOptionLocator: Locator;
  readonly deleteSelectedPreferenceOptionLocator: Locator;
  readonly deleteSelectedPreferenceConfirmLocator: Locator;
  readonly preferenceNameTextBoxLocator: Locator;
  readonly setDefaultPreferenceCheckboxLocator: Locator;
  readonly savePreferenceButtonLocator: Locator;
  readonly statusDropdowninAllowancesview: Locator;
  readonly preferenceMenuIcon: Locator;
  readonly preferenceSaveAsNewButton: Locator;
  readonly preferenceSaveButton: Locator;
  readonly preferencesDropdown: Locator;
  readonly preferencesDeleteSelectedButton: Locator;
  readonly updatePreferenceButton: Locator;
  readonly eventStatusDropdown: Locator;
  readonly applyButtoninDropdowns: Locator;
  readonly allowanceTypeDropdowninAllowanceview: Locator;
  readonly performanceTypeDrodowninAllowanceview: Locator;
  readonly dateTypeDropdowninAllowanceView: Locator;
  readonly vehicleTypeDropdowninAllowancesview: Locator;
  readonly applyButtoninDropdownswithName: Locator;
  readonly savePreferenceInputLocator: Locator;
  readonly confirmButtonInModals: Locator;
  readonly filterTypesDropdownLocator: Locator;
  readonly filterTypesAlertsViewDropdownLocator: Locator;
  readonly selectallFilterTypesDropdownLocator: Locator;
  readonly applyButtonFilterTypesDropdownLocator: Locator;
  readonly filterByTypeButtonInTasksViewLocator: Locator;
  readonly planningTableLocator: Locator;
  readonly tableRowsLocator: Locator;
  readonly tableHeaderCellsLocator: Locator;
  readonly selectAllInFilterTypeDropDownInTasksViewLocator: Locator;
  readonly selectNewItemFilterTypeInTaskViewLocator : Locator;
  readonly selectNewItemFilterTypeInAlertViewLocator: Locator;
  readonly selectVendorEventStartItemInAlertSortDropDownLocator: Locator;
  readonly clickAlertViewSortDropDownLocator : Locator;
  readonly eventPublishedOptionInEventStatusDropdownAlertsView: Locator;
  preferenceName: string = "";

  constructor(page: Page) {
    this.page = page;
    this.viewDropdown = this.page.locator('#abs-select-dropdown1-abs-promotion-header-select-dropdown-abs-pm-promotion-header');
    this.viewDropdownPlanningOption = this.page.getByText("Planning");
    this.viewDropdownAlertsOption = this.page.getByText("Alerts");
    this.defaultSelectedSearchCriteriaOptionDropdownLocator =
      this.page.getByRole("button", { name: "Offer ID#" });
    this.searchTextboxLocator = this.page.getByRole("textbox", {
      name: "Search",
    });
    this.searchIconLocator = this.page.locator(".lucide.lucide-search");
    this.eventRecordLocator = this.page.locator(
      '(//a[@data-testid="link-by-product-d"])[1]'
    );
    this.savedPreferencesDropdownLocator = this.page
      .getByTestId("promo-header")
      .locator("svg");
    this.testPreferenceLocator = this.page.locator("div").filter({
      hasText: PromotionManagementPageEnum.playwrightTestPreference,
    });
    this.preferenceSettingsMenuLocator = this.page.getByRole("img", {
      name: "ellispe",
    });
    this.updatePreferenceOptionLocator =
      this.page.getByText("Update Preference");
    this.saveAsNewPreferenceOptionLocator = this.page.getByText("Save As New");
    this.deleteSelectedPreferenceOptionLocator =
      this.page.getByText("Delete Selected");
    this.deleteSelectedPreferenceConfirmLocator = this.page.getByRole(
      "button",
      { name: "Confirm" }
    );
    this.preferenceNameTextBoxLocator = this.page.getByRole("textbox", {
      name: "preference1",
    });
    this.setDefaultPreferenceCheckboxLocator = this.page.getByRole("checkbox");
    this.savePreferenceButtonLocator = this.page.getByRole("button", {
      name: "Save",
    });
    this.statusDropdowninAllowancesview = this.page.locator(
      '//*[@id="abs-select-checkbox1"]/span/span[2]'
    );
    this.preferenceMenuIcon = this.page.getByRole("img", { name: "ellispe" });
    this.preferenceSaveAsNewButton = this.page.getByText("Save As New");
    this.preferenceSaveButton = this.page.locator('//div[@data-testid="modal-id"]//div[5]//button[2]');
    this.preferencesDropdown = this.page.locator(
      "#abs-render-promotion-header2-abs-render-promo-header-abs-pm-promotion-header"
    );
    this.preferencesDeleteSelectedButton =
      this.page.getByText("Delete Selected");
    this.updatePreferenceButton = this.page.getByText("Update Preference");
    this.eventStatusDropdown = this.page.getByText(/^Event Status: /);
    this.applyButtoninDropdowns = this.page.getByText("Apply");
    this.allowanceTypeDropdowninAllowanceview =
      this.page.getByText("Allw Type:");
    this.performanceTypeDrodowninAllowanceview =
      this.page.getByText("Performance:");
    this.dateTypeDropdowninAllowanceView = this.page.getByRole("button", {
      name: /^Date Type/,
    });
    this.vehicleTypeDropdowninAllowancesview =
      this.page.getByText("Vehicle Type:");
    this.applyButtoninDropdownswithName = this.page.getByText("Apply", {
      exact: true,
    });
    this.savePreferenceInputLocator = this.page.getByRole("textbox", {
      name: "preference1",
    });
    this.confirmButtonInModals = this.page.getByRole("button", {
      name: "Confirm",
    });
    this.filterTypesDropdownLocator = this.page.locator("//div[@id='abs-filter-popover-wrapper4']");
    this.filterTypesAlertsViewDropdownLocator = this.page.locator('//*[@id="render-header-item-select-taskStatusType"]//button');
    this.selectallFilterTypesDropdownLocator = this.page.locator('#abs-filter-select-all-wrapper-item-filterByTaskType div').nth(2);
    this.applyButtonFilterTypesDropdownLocator = this.page.getByTestId('apply-btn');
    this.statusDraftOptionInPlanningViewLocator = this.page
      .locator(".flex.gap-3 > div > .flex.flex-col > div > .flex")
      .first();
    this.statusAgreedOptionInPlanningViewLocator = this.page.locator(
      "div:nth-child(4) > div > .flex.flex-col > div > .flex"
    );
    this.statusDropdownToggleOptionInPlanningViewLocator = this.page
      .locator("#abs-multi-select-dropdown0")
      .getByRole("img");
    this.filterByTypeButtonInTasksViewLocator = this.page.locator(
      "#abs-filter-popover-wrapper2"
    );
    this.selectAllInFilterTypeDropDownInTasksViewLocator = this.page
      .locator("#abs-filter-select-all-wrapper-item-filterByTaskType div")
      .nth(2);
    this.planningTableLocator = this.page.locator(
      '//table[@id="planning-table"]'
    );
    this.tableRowsLocator = this.page.locator(
      '//table[@id="planning-table"]/tbody/tr'
    );
    this.tableHeaderCellsLocator = this.page.locator(
      '//table[@id="planning-table"]/thead/tr/th/div'
    );
    this.selectNewItemFilterTypeInTaskViewLocator = this.page.locator('[id="abs-filter-item-list-label-wrapper-New Item-filterByTaskType-3"] div').nth(2);
    this.selectNewItemFilterTypeInAlertViewLocator = this.page.locator('//div[@id="abs-select-dropdown3"]/div[@id="abs-select-dropdown-option0"][4]');
    this.eventPublishedOptionInEventStatusDropdownAlertsView = this.page.locator('//div[@id="abs-select-dropdown3"]/div[@id="abs-select-dropdown-option0"][7]');
    this.clickAlertViewSortDropDownLocator = this.page.locator('[id="abs-select-dropdown1-render-header-item-select-tasksSortType"]');
    this.selectVendorEventStartItemInAlertSortDropDownLocator = this.page.locator('//div[@id="abs-select-dropdown3"]//div[@data-testid="dropdown-option-Vendor + Event Start"]//div[@data-testid="abs-select-dropdown-option2"]');
  }

  async navigateToPromotionManagement() {
    await this.page.goto(
      "https://memsp-qa2.albertsons.com/memsp-ui-shell/meupp/"
    );
  }

  async verifyPromotionManagementHeading() {
    await expect(
      this.page.getByRole("heading", { name: "Promotion Management" })
    ).toBeVisible();
  }

  async clickAddEvent() {
    await this.page.getByRole("link", { name: "Add Event" }).click();
  }

  async verifyNewEventVisible() {
    await expect(this.page.getByText("New Event")).toBeVisible();
  }

  async selectNationalEvent() {
    await expect(
      this.page.getByText("National", { exact: true })
    ).toBeVisible();
    await this.page.getByRole("button", { name: "Get Started" }).nth(2).click();
  }

  async searchPromoProductGroup() {
    await this.page
      .getByRole("textbox", { name: "Search Promo Product Groups" })
      .click();
    await this.page.getByText("749293 - ACT Dry Mouth CIG").click();
  }
  async getEventIdsFromTable(): Promise<void> {
    try {
      const { page } = this;
      await this.planningTableLocator.waitFor({
        state: "visible",
        timeout: 10000,
      });
      await page.waitForLoadState("networkidle");
      const headerCells = await this.tableHeaderCellsLocator.all();
      let eventColIndex = -1;

      for (let i = 0; i < headerCells.length; i++) {
        const headerText = (await headerCells[i].innerText()).trim();
        if (headerText === "Event") {
          eventColIndex = i;
          break;
        }
      }
      if (eventColIndex === -1) {
        throw new Error('"Event" column not found in table headers');
      }
      const rows = await this.tableRowsLocator.all();
      const eventIds: string[] = [];

      for (const row of rows) {
        const cells = await row.locator("td").all();

        if (cells.length > eventColIndex) {
          const cellText = (await cells[eventColIndex].innerText()).trim();
          const eventId = cellText.split("-")[0].trim();
          if (eventId) {
            eventIds.push(eventId);
          }
        }
      }
      EventManagementPage.tableEventIds = eventIds;
      expect(
        eventIds.length,
        "No event IDs found in Event column"
      ).toBeGreaterThan(0);
    } catch (error: any) {
      console.error("Error while extracting event IDs:", error);
      throw new Error(
        `Failed to get event IDs from Event column: ${error.message}`
      );
    }
  }

  async selectPromoCycle() {
    await this.page
      .getByRole("button", { name: "Vehicle Type/Custom Date" })
      .click();
    await this.page.getByText("Promo Cycle").click();
    await this.page.getByRole("button", { name: "Start Week/Vehicle" }).click();
    await this.page.getByText("GMHBC P05 2025 - 06/18/25 - 07/15/").click();
  }

  async saveEventDetails() {
    await this.page
      .getByRole("button", { name: "Save Event Details & Add" })
      .click();
  }

  async handleWarningPopup() {
    await expect(this.page.getByText("Warning:")).toBeVisible();
    await expect(
      this.page.getByText("The following divisions do")
    ).toBeVisible();
    await expect(this.page.getByText("- United")).toBeVisible();
    await expect(
      this.page.getByText("Do you want to proceed with")
    ).toBeVisible();
    await this.page.getByRole("button", { name: "OK" }).click();
  }

  async verifyNewOfferVisible() {
    await expect(this.page.getByText("New Offer")).toBeVisible();
  }

  async fillAllowanceDetails() {
    await this.page.locator('button[name="allowanceType"]').click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Case$/ })
      .nth(2)
      .click();
    await this.page
      .getByRole("button", { name: "Review Overlaps & Enter" })
      .click();
    await expect(
      this.page.getByText("Allowance amount changes for")
    ).toBeVisible();
    await this.page.getByRole("button", { name: "OK" }).click();
  }

  async updateAllowanceAmount() {
    await this.page.getByRole("spinbutton").click();
    await this.page.getByRole("spinbutton").fill("2");
    await this.page
      .getByRole("button", { name: "Update All Divisions" })
      .click();
    await this.page.getByRole("button", { name: "Save All Changes" }).click();
  }

  async clickViewDropdown() {
    await this.viewDropdown.click();
  }

  async selectViewDropdownOption(option: string) {
    if (option === "Tasks") {
      await this.page.getByTestId("dropdown-option-Tasks").getByText(option);
    } else {
      await this.page.getByText(option).click();
    }
  }

  async clickOfferIdSearchCriteriaDropdown() {
    await this.defaultSelectedSearchCriteriaOptionDropdownLocator.click();
  }

  async selectSearchCriteriaOption(option: string) {
    await this.page
      .locator(
        `//div[@id="abs-select-dropdown-option2" and text()='${option}']`
      )
      .click();
  }

  async enterEventIdInSearchCriteria() {
    const searchInput = this.searchTextboxLocator;
    const eventManagementPage = new EventManagementPage(pageFixture.page);
    if (await eventManagementPage.getEventId()) {
      await searchInput.fill(await eventManagementPage.getEventId());
    } else {
      await searchInput.fill(EventManagementPage.tableEventIds[0]);
    }
  }
   async enterEventNameInSearchCriteria() {
      const searchInput = this.searchTextboxLocator;
      await searchInput.fill(EventManagementPage.tableEventNames[0]);
    }

     async getEventNamesFromTable(): Promise<void> {
      try {
        const { page } = this;
        await this.planningTableLocator.waitFor({
          state: "visible",
          timeout: 10000,
        });
        await page.waitForLoadState("networkidle");
        const headerCells = await this.tableHeaderCellsLocator.all();
        let eventColIndex = -1;
        for (let i = 0; i < headerCells.length; i++) {
          const headerText = (await headerCells[i].innerText()).trim();
          if (headerText === "Event") {
            eventColIndex = i;
            break;
          }
        }
  
        if (eventColIndex === -1) {
          throw new Error('"Event" column not found in table headers');
        }
  
        const rows = await this.tableRowsLocator.all();
        const eventNames: string[] = [];
  
        for (const row of rows) {
          const cells = await row.locator("td").all();
  
          if (cells.length > eventColIndex) {
            const cellText = (await cells[eventColIndex].innerText()).trim();
            const withoutId = cellText
              .substring(cellText.indexOf("-") + 1)
              .trim();
            const eventName = withoutId.split(" - ")[0].trim();
  
            if (eventName) {
              eventNames.push(eventName);
            }
          }
        }
        EventManagementPage.tableEventNames = eventNames;
  
        expect(
          eventNames.length,
          "No event names found in Event column"
        ).toBeGreaterThan(0);
      } catch (error: any) {
        console.error("Error while extracting event names:", error);
        throw new Error(
          `Failed to get event names from Event column: ${error.message}`
        );
      }
    }
  async clickSearchIcon() {
    await this.searchIconLocator.click();
  }

  async clickNationalEventRecordInSearchResult() {
    await this.page.waitForTimeout(2000);
    await this.eventRecordLocator.waitFor({ state: "visible", timeout: 5000 });
    await this.eventRecordLocator.click();
  }
  async verifyNoRecordsFound(expectedMessage: string) {
    await this.page.waitForTimeout(2000);
    const noRecordsText = this.page.locator(`text=${expectedMessage}`);
    await expect(noRecordsText).toBeVisible();
}

  async clickPreferenceDropDown() {
    await this.savedPreferencesDropdownLocator.click();
  }

  async selectExistingPreference() {
    if ((await this.testPreferenceLocator.count()) > 0) {
      await this.testPreferenceLocator.first().click();
    }
  }

  async clicksPrefEllipses() {
    await this.preferenceSettingsMenuLocator.click();
  }

  async selectsUpdatePreference() {
    await this.updatePreferenceOptionLocator.click();
    this.checkPreferenceNameAndFill();
  }

  async selectsSaveAsNewPref() {
    await this.saveAsNewPreferenceOptionLocator.click();
  }

  async deleteSelectedPreference() {
    await this.deleteSelectedPreferenceOptionLocator.click();
    await this.deleteSelectedPreferenceConfirmLocator.click();
  }

  async entersNewPrefName() {
    await this.preferenceNameTextBoxLocator.click();
    await this.preferenceNameTextBoxLocator.fill(
      PromotionManagementPageEnum.playwrightTestPreference
    );
  }

  async checkPreferenceNameAndFill() {
    const textbox = this.preferenceNameTextBoxLocator;
    const value = await textbox.inputValue();
    if (!value) {
      this.entersNewPrefName();
    }
  }

  async selectSetAsDefaultPref() {
    await this.setDefaultPreferenceCheckboxLocator.check();
  }

  async savePref() {
    await this.savePreferenceButtonLocator.click();
  }
  async selectOrUnselectAll() {
    const selectUnSelectAllButton = this.page.getByText("Select/Deselect All", {
      exact: false,
    });
    await selectUnSelectAllButton.waitFor({ state: "visible", timeout: 5000 });
    await selectUnSelectAllButton.click();
  }
  async openingStatusDropdowninAllowanceView() {
    await this.statusDropdowninAllowancesview.waitFor({
      state: "visible",
      timeout: 5000,
    });
    await this.statusDropdowninAllowancesview.click();
  }

  async clickApplyButtoninDropdowns() {
    await this.applyButtoninDropdowns.click();
  }

  async clickApplyButtoninDropdownswithName() {
    await this.applyButtoninDropdownswithName.click();
  }
  async selectingEventStatusinAllowanceView(eventStatus: string) {
    const statusOption = this.page.getByText(eventStatus, { exact: false });
    await statusOption.waitFor({ state: "visible", timeout: 3000 });
    await statusOption.click();
  }

  async openAllowanceTypeDropdowninAllowanceView() {
    await this.allowanceTypeDropdowninAllowanceview.click();
  }
  async selectingAllowanceTypeinAllowanceView(allowanceType: string) {
    await this.page.getByTestId("popper").getByText(allowanceType).click();
  }

  async openingPerformanceTypeDropdowninAllowanceView() {
    await this.performanceTypeDrodowninAllowanceview.waitFor({
      state: "visible",
      timeout: 5000,
    });
    await this.performanceTypeDrodowninAllowanceview.click();
  }

  async selectingPerformaceTypeinAllowanceView(performanceType: string) {
    await this.page.getByText(performanceType).click();
  }

  async openingDateTypeDropdowninAllowanceView() {
    await this.dateTypeDropdowninAllowanceView.click();
  }

  async selectingDateTypesinAllowanceView(dateType: string) {
    await this.page.getByText(dateType).click();
  }

  async openingVehicleTypeDropdowninAllowanceView() {
    await this.vehicleTypeDropdowninAllowancesview.click();
  }

  async selectingVehicleTypeinAllowanceView(vehicleType: string) {
    await this.page.getByText(vehicleType).click();
  }

  async clickFilterTypesDropdown() {
    await this.filterTypesDropdownLocator.waitFor({
      state: "visible", timeout: 5000
    })
    await this.filterTypesDropdownLocator.click();
  }

  async clickFilterTypesDropdownInAlertsView() {
    const chevron = this.page.locator('#render-header-item-select-taskStatusType button svg.lucide-chevron-down');
    await chevron.waitFor({ state: 'visible' });
    await chevron.click();
  }
  
  async selectAllFilterTypesDropdown() {
    await this.selectallFilterTypesDropdownLocator.click();
  }

    async clickApplyButtonInFilterTypesDropdown() {
    await this.applyButtonFilterTypesDropdownLocator.click();
  }

  async openPreferencesMenu() {
    await this.preferenceMenuIcon.click();
  }

  async clickSaveAsNewPreference() {
    await this.preferenceSaveAsNewButton.click();
  }

  async clickpreferenceSaveButton() {
    await this.preferenceSaveButton.click();
  }

  async saveNewPreferenceFlow(setAsDefault: boolean) {
    this.preferenceName = `playwright-${Date.now()}`;
    const textbox = this.savePreferenceInputLocator;
    await textbox.click();
    await textbox.fill(this.preferenceName);
    if (setAsDefault) {
      await this.page.getByRole("checkbox").check();
    }
    await this.clickpreferenceSaveButton();
  }

  async openClosePreferencesDropdown() {
    await this.preferencesDropdown.click();
  }

  async isDefaultPreferenceExists() {
    const defaultPref = this.page.getByText("Default", { exact: true });
    const exists = await defaultPref.isVisible();
    return exists;
  }
  async deletingPreviousDefaultPreference() {
    await this.deletePreference(true);
  }

  async deletePreference(deletePreviousDefault: boolean) {
    await this.openClosePreferencesDropdown();
    if (this.preferenceName) {
      const matchingPreferences = await this.page
        .getByText(this.preferenceName, { exact: true })
        .all();
      if (matchingPreferences && matchingPreferences.length > 0) {
        await matchingPreferences[0].click();
      }
    }
    if (deletePreviousDefault) {
      const allPreferences = this.page.locator(
        '[data-testid="popper"] .flex.items-center'
      );

      // Filter out items that have the "Default" label
      const nonDefaultPreference = allPreferences.filter({
        hasNot: this.page.locator("span", { hasText: "Default" }),
      });

      const count = await nonDefaultPreference.count();

      if (count > 0) {
        const firstNonDefault = nonDefaultPreference.first();
        await firstNonDefault.waitFor({ state: "visible", timeout: 5000 });
        await firstNonDefault.scrollIntoViewIfNeeded();
        await firstNonDefault.click();
      } else {
        console.log("⚠️ No non-default preference found. Skipping deletion.");
        return;
      }
    }
    await this.openPreferencesMenu();
    const deletSelectedButton = this.preferencesDeleteSelectedButton;
    try {
      await expect(deletSelectedButton).toBeVisible({ timeout: 10000 });
      await deletSelectedButton.click();
    } catch (e) {
      console.warn("Delete button not visible or not clickable");
      return;
    }
    const confirmButton = this.confirmButtonInModals;
    try {
      await expect(confirmButton).toBeVisible({ timeout: 10000 });
      await confirmButton.click();
    } catch (e) {
      console.warn("Confirm button not visible or not clickable");
    }
  }

  async selectingView(viewName: string) {
    await this.page.getByText(viewName, { exact: true }).click();
  }
  async clickOnFilterByTypeInTasksView() {
    await this.filterByTypeButtonInTasksViewLocator.waitFor({
      state: "visible",
    });
    await this.filterByTypeButtonInTasksViewLocator.click();
  }
  async selectsAllInFilterByTypeDropdownInTasksView() {
    await this.selectAllInFilterTypeDropDownInTasksViewLocator.waitFor({
      state: "visible",
    });
    await this.selectAllInFilterTypeDropDownInTasksViewLocator.click();
  }

  async selectingEventType(eventType: string) {
    const wrapper = this.page.locator("#eventType-NDP");

    const nationalOption = wrapper.locator(`div:has(span[title=${eventType}])`);
    const checkbox = nationalOption.locator(
      'input[data-testid="filter-checkbox"]'
    );

    if (eventType && !(await checkbox.isChecked())) {
      const option = this.page.getByText(eventType, { exact: true });
      await option.waitFor({ timeout: 10000 });
      await option.click();
    }
  }

  async clickUpdatePreference() {
    await this.updatePreferenceButton.click();
  }

  async clickUpdatePreferenceButton() {
    await this.clickUpdatePreference();
  }

  async selectingSavedPreference() {
    await this.openClosePreferencesDropdown();
    await this.page.getByText(this.preferenceName, { exact: true }).click();
  }

  async selectingEventStatusDropdownInPlanningView() {
    await this.eventStatusDropdown.click();
  }

  async togglingEventStatusSelectUnselectAll() {
    await this.page.locator("#abs-multi-select-dropdown2 div").nth(2).click();
  }

  async selectingEventStatusasDraft() {
    await this.page
      .locator(".flex.gap-3 > div > .flex.flex-col > div > .flex")
      .first()
      .click();
  }
  async clickonEventStatusDropdownInPlanningView() {
    await this.statusDropdownToggleOptionInPlanningViewLocator.click();
  }
  async selectEventStatusInPlanningView(statusType: string): Promise<void> {
    if (statusType === "Draft") {
      await this.statusDraftOptionInPlanningViewLocator.click();
    } else if (statusType === "Agreed") {
      await this.statusAgreedOptionInPlanningViewLocator.click();
    } else {
      throw new Error(`Unsupported statusType: ${statusType}`);
    }

    await this.page.waitForTimeout(1000);
  }
  async clickUpdateButtonByLabel(label: string): Promise<void> {
    await this.page.getByText(label, { exact: true }).click();
  }
  async selectNewItemFilterTypeInTasksView() {
    await this.selectNewItemFilterTypeInTaskViewLocator.click();
  }
  async selectNewItemFilterTypeInAlertView() {
    await this.page.waitForTimeout(1000);
    await this.selectNewItemFilterTypeInAlertViewLocator.click();
  }
  
  async selectSortDropdown() {
    await this.clickAlertViewSortDropDownLocator.click();
  }
  async selectVendorEventStartSortOptionFromDropdown(){
    await this.selectVendorEventStartItemInAlertSortDropDownLocator.click();
  }

  async selectEventPublishedOptionInEventStatusDropdownInAlertsView() {
    await this.eventPublishedOptionInEventStatusDropdownAlertsView.click();
  }

  async verifyEventInTable() {
    const eventManagementPage = new EventManagementPage(pageFixture.page);
    const eventRow = this.page.getByText(await eventManagementPage.getEventId(),{exact:false});
    await eventRow.isVisible();
  }
}