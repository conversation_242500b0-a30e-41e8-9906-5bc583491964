import { expect, Page } from "playwright/test";

export class EventCreationWithPID {
    constructor(private page: Page) {}

    async populatePID(pidData:any) {
        await this.page.getByText(pidData?.label).click();
        await this.page.getByRole('spinbutton').fill(pidData.value);
        // await this.page.getByText('Search PID').click();
        await this.page.getByRole('button', { name: pidData?.fetchPID }).click();
    }

    async populateEventName(eventName:any) {
        await this.page.locator('input[name="name"]').waitFor({ timeout: 5000 });

        // Assert pre-populated values after Fetch Event Details
        // await expect(this.page.locator('input[name="name"]')).toHaveValue(
        //   /GM- ROC RETINOL CORREXION LINE SMOOTHING MAX CIG 513699/,
        // );
        await expect(this.page.locator('input[name="name"]')).toHaveValue(
            new RegExp(eventName, 'i'), // 'i' makes the regex case-insensitive
        );
    }

    async populateStoreGroupsData(storeGroupsData:any) {
        if (!storeGroupsData?.type || !storeGroupsData?.label) {
            throw new Error('Store group data must include type and label');
        }

        console.log("Verifying store group data:", storeGroupsData);
        
        // Verify store group type
        const storeGroupTypeElement = this.page.getByRole('button', { name: storeGroupsData.label }).locator('..');
        await storeGroupTypeElement.waitFor({ state: 'visible', timeout: 30000 });
        await expect(storeGroupTypeElement).toContainText(storeGroupsData.type);

        // Verify store groups value if provided
        if (storeGroupsData?.value) {
            await expect(this.page.getByRole('button', { name: storeGroupsData.value })).toBeVisible();
        }
    }

    async populateVehicleTypeOrCustomDate(vehicleType:any) {
        // Vehicle-related dropdowns
        await this.page.getByRole('button', { name: 'Vehicle Type/Custom Date' }).click();
        if (vehicleType === 'Custom Date') {
            await this.page.getByText('Custom Date').click();
        } else if (vehicleType === 'Promo Cycle') {
            await this.page.getByText('Promo Cycle').click();
        }
    }

    async yearSelection(yearData:any) {
        // await expect(this.page.locator('text=2024')).toBeVisible();
        await expect(
            this.page.getByRole('button', { name: yearData?.label }).locator('..')
        ).toContainText(yearData?.value);

    }

    async weekSelection(weekData:any) {
        console.log("weekData", weekData);
        await expect(
            this.page.getByRole('button', { name: 'Start Week/Vehicle' }).locator('..')
        ).toContainText(weekData);
    }

    async vehicleStartSelection(startDate: string) {
        await expect(this.page.getByText('Vehicle Start')).toBeVisible();
        await expect(
            this.page.locator('#abs-input-date-picker-uds-error > .relative').nth(0)
        ).toContainText(startDate);
    }

    async vehicleEndSelection(endDate: string) {
        await expect(this.page.getByText('Vehicle End')).toBeVisible();
        await expect(
            this.page.locator(
                'div:nth-child(5) > #abs-input-date-picker-uds-main > #abs-input-date-picker-uds-controler > #abs-input-date-picker-uds-error > .relative'
            )
        ).toContainText(endDate);
    }

    async saveEventDetails() {
        // Save the event
        await this.page.waitForTimeout(1000);
        await this.page.getByRole('button', { name: /Save Event Details & Add/ }).click();
    }

}