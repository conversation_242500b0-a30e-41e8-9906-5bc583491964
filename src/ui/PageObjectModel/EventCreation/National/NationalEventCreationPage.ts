import { expect, Page, Locator } from "@playwright/test";
import { VehicleComponent } from "../../../Components/baseComponent/EventFields/Vehicle/VehicleComponent";
import { StoreGroupComponent } from "../../../Components/baseComponent/EventFields/StoreGroup/StoreGroupComponent";
import { ProductGroupComponent } from "../../../Components/baseComponent/EventFields/ProductGroup/ProductGroupComponent";

export class NationalEventCreationPage {
    private storeGroup: StoreGroupComponent;
    private productGroup: ProductGroupComponent;
    private vehicle: VehicleComponent;
    public static popperDivisions: string[] = [];
    public static tableDivisions: string[];
    readonly divisionCountLocator: Locator;
    readonly popperLocator: Locator;
    readonly tableDivisionLocator: Locator;
    readonly popperDivisionLocator: Locator;
    readonly pidInputLocator: Locator;
    readonly pidErrorLocator: Locator;
    readonly fetchEventDetailsButtonLocator: Locator;
    readonly selectionDropdownButtonLocator: Locator;
    readonly promoProductGroupSearchBoxLocator: Locator;
    readonly productGroupNameLocator: Locator;
    readonly storeGroupTypeButtonLocator: Locator;
    readonly vehicleTypeButtonLocator: Locator;
    readonly startWeekButtonLocator: Locator;
    readonly eventNameInputLocator: Locator;
    readonly storeGroupItemLocator: Locator;

    constructor(readonly page: Page) {
        this.storeGroup = new StoreGroupComponent(page);
        this.productGroup = new ProductGroupComponent(page);
        this.vehicle = new VehicleComponent(page);
        this.divisionCountLocator = this.page.locator("//span[contains(@class,'self-center text-sm font-semibold')]/p");
        this.popperLocator = this.page.locator("div[data-testid='popper']");
        this.tableDivisionLocator = this.page.locator("//td[contains(@class, 'h-[inherit]')]//div[@id='abs-allowance-view-column-config-offerId']");
        this.popperDivisionLocator = this.page.locator("//div[@data-testid='popper']/span/div");
        this.pidInputLocator = page.getByTestId('periscope-id');
        this.pidErrorLocator = page.getByTestId('periscope-error');
        this.fetchEventDetailsButtonLocator = page.getByRole('button', { name: 'Fetch Event Details' });
        this.selectionDropdownButtonLocator = page.getByTestId('selection-dropdown-button');
        this.promoProductGroupSearchBoxLocator = page.getByRole('textbox', { name: 'Search Promo Product Groups' });
        this.productGroupNameLocator = page.getByTestId('promo-product-group-name');
        this.storeGroupTypeButtonLocator = page.getByRole('button', { name: 'Store Group Type' });
        this.vehicleTypeButtonLocator = page.getByRole('button', { name: 'Vehicle Type/Custom Date' });
        this.startWeekButtonLocator = page.getByRole('button', { name: 'Start Week/Vehicle' });
        this.eventNameInputLocator = page.getByTestId('event-name');
        this.storeGroupItemLocator = page.locator('div.itemWrap');
    }

    async populateStoreGroupsData(storeGroupData: any) {
        try {
            // First get the store group type
            if (storeGroupData?.type) {
                await this.storeGroup.getStoreGroupType({
                    label: "Store Group Type",
                    type: storeGroupData.type
                });
            }

            // Then get store groups data
            if (storeGroupData?.value) {
                await this.storeGroup.getStoreGroups({
                    label: storeGroupData.value,
                    // Only pass totalStores if it exists
                    ...(storeGroupData.totalStores && { totalStores: storeGroupData.totalStores })
                });
            }
        } catch (error: any) {
            console.error('Error in populateStoreGroupsData:', error);
            throw new Error(`Failed to populate store group data: ${error.message}`);
        }
    }

    async populateProductGroupNational(ppgData: any) {
        await this.productGroup.selectProductGroup(ppgData);
    }

    async populateVehicleTypeOrCustomDateNational(option: 'Other' | 'Promo Cycle' | 'Custom Date') {
        try {
            // Select vehicle type
            await this.vehicle.selectVehicleType(option);

            if (option === 'Promo Cycle') {
                // For Promo Cycle, select the start week and verify dates
                const weekData = '4wk Feature Wk 35 2025 - 08/27/25 - 09/23/25';
                await this.vehicle.selectStartWeek(weekData);
                await this.vehicle.verifyStartDate('08/27/25');
                await this.vehicle.verifyEndDate('09/23/25');
            } else if (option === 'Custom Date') {
                // For Custom Date, dates are auto-selected in the component
                const dropdown = this.page.getByRole('button', { name: 'Start Week/Vehicle' });
                await expect(dropdown).toBeVisible();
                await expect(dropdown).toHaveText(/Other/);
            }
        } catch (error: any) {
            console.error('Error in populateVehicleTypeOrCustomDateNational:', error);
            throw new Error(`Failed to populate vehicle type: ${error.message}`);
        }
    }

    async saveEventDetails() {
        await this.page.getByRole('button', { name: 'Save Event Details & Add' }).click();
        await expect(this.page.getByRole('alert')).toBeVisible();
        await expect(this.page.getByRole('alert').getByRole('img')).toBeVisible();
        await expect(this.page.getByText('The following divisions do')).toBeVisible();
        await expect(this.page.getByRole('button', { name: 'Remove Invalid Divisions' })).toBeVisible();
        await this.page.getByRole('button', { name: 'Remove Invalid Divisions' }).click();
        await expect(this.page.getByRole('alert')).not.toBeVisible();
        await this.page.getByRole('button', { name: 'Save Event Details & Add' }).click();
    }
    async clickDivisionCount() {
        try {
            await this.divisionCountLocator.click();
            await this.popperLocator.waitFor({ state: 'visible', timeout: 6000 });
        } catch (error: any) {
            console.error('Error clicking division count:', error);
            throw new Error(`Failed to click division count: ${error.message}`);
        }
    }

    async getDivisionsList(): Promise<string[]> {
        try {
            // Get all division option elements inside the popper
            const divisionElements = await this.popperDivisionLocator.all();

            if (divisionElements.length === 0) {
                console.warn('No division elements found in the popper');
                return [];
            }

            // Clear the existing divisions list
            const divisions: string[] = [];

            // Loop through each division element
            for (const element of divisionElements) {
                const text = await element.innerText();
                // Split by 'All' and take the first part
                const division = text.split('All')[0].trim();
                if (division) {
                    divisions.push(division);
                }
            }

            // Store in static property for later use
            NationalEventCreationPage.popperDivisions = divisions;

            console.log(`Found ${divisions.length} divisions:`, divisions);
            return divisions;
        } catch (error: any) {
            console.error('Error while getting divisions:', error);
            throw new Error(`Failed to get divisions: ${error.message}`);
        }
    }

    async getDivisionsFromTable(): Promise<void> {
        try {
            // Wait for table to be visible and network to be idle
            await this.page.waitForSelector('text=Division Level Breakdown', { state: 'visible', timeout: 30000 });
            await this.page.waitForLoadState('networkidle');
            
            // Add additional wait to ensure table is fully rendered
            await this.page.waitForTimeout(2000);
            
            // Wait for first cell and ensure it's stable
            await this.tableDivisionLocator.first().waitFor({ state: 'visible', timeout: 30000 });
            
            // Get all division cells
            const divisionCells = await this.tableDivisionLocator.all();
            const divisions: string[] = [];

            // Process each cell with additional stability checks
            for (const cell of divisionCells) {
                    await cell.waitFor({ state: 'visible', timeout: 5000 });
                    await this.page.waitForTimeout(500); // Small pause for stability
                    await cell.scrollIntoViewIfNeeded({ timeout: 5000 });
                    const text = await cell.innerText();

                // Split by hyphen and take the second part (division name)
                const parts = text.split('-');
                if (parts.length > 1) {
                    const divisionName = parts[1].trim();
                    if (divisionName) {
                        divisions.push(divisionName);
                    }
                }
            }

            // Store in static property for later use
            NationalEventCreationPage.tableDivisions = divisions;

            // Verify we found some divisions
            expect(divisions.length, 'No divisions found in the table').toBeGreaterThan(0);
        } catch (error: any) {
            console.error('Error while getting divisions from table:', error);
            throw new Error(`Failed to get divisions from table: ${error.message}`);
        }
    }
    async compareDivisionLists(): Promise<void> {
        try {
            // Use the stored division lists
            const popperDivisions = NationalEventCreationPage.popperDivisions;
            const tableDivisions = NationalEventCreationPage.tableDivisions;

            // Normalize both arrays: convert to lowercase and fix truncated names
            const normalizeAndSort = (divisions: string[]) => {
                return divisions.map(d => {
                    d = d.toLowerCase().trim();
                    // Handle truncated names
                    if (d === 'mid') d = 'mid-atlantic';
                    return d;
                }).sort();
            };

            const normalizedPopper = normalizeAndSort(popperDivisions);
            const normalizedTable = normalizeAndSort(tableDivisions);

            // Convert arrays to strings for exact comparison
            const popperString = JSON.stringify(normalizedPopper);
            const tableString = JSON.stringify(normalizedTable);

            const match = popperString === tableString;

            // For debugging, if they don't match, show the differences
            if (!match) {
                const popperSet = new Set(normalizedPopper);
                const tableSet = new Set(normalizedTable);

                const missingInPopper = [...tableSet].filter(x => !popperSet.has(x));
                const missingInTable = [...popperSet].filter(x => !tableSet.has(x));
                throw new Error(`Division mismatch found:
                    Missing from popper: ${missingInPopper.join(', ')}
                    Missing from table: ${missingInTable.join(', ')}`);
            }
        } catch (error: any) {
            console.error('Error while comparing division lists:', error);
            throw error;
        }
    }
    async verifyPromoProductGroupSearchBoxVisible() {
        await expect(this.promoProductGroupSearchBoxLocator).toBeVisible();
    }
    async verifyPromoProductGroupSearchBoxEnabled() {
        await expect(this.promoProductGroupSearchBoxLocator).toBeEnabled();
    }

    async verifyProductGroupNameVisible() {
        await expect(this.productGroupNameLocator).toBeVisible();
    }

    async verifyStoreGroupTypeButtonVisible() {
        await expect(this.storeGroupTypeButtonLocator).toBeVisible();
    }
    async verifyStoreGroupTypeButtonEnabled() {
        await expect(this.storeGroupTypeButtonLocator).toBeEnabled();
    }

    async verifySelectionDropdownButtonVisible() {
        await expect(this.selectionDropdownButtonLocator).toBeVisible();
    }
    async verifySelectionDropdownButtonEnabled() {
        await expect(this.selectionDropdownButtonLocator).toBeEnabled();
    }

    async verifyVehicleTypeButtonVisible() {
        await expect(this.vehicleTypeButtonLocator).toBeVisible();
    }
    async verifyVehicleTypeButtonEnabled() {
        await expect(this.vehicleTypeButtonLocator).toBeEnabled();
    }

    async verifyStartWeekButtonVisible() {
        await expect(this.startWeekButtonLocator).toBeVisible();
    }
    async verifyStartWeekButtonEnabled() {
        await expect(this.startWeekButtonLocator).toBeEnabled();
    }

    async verifyEventNameInputVisible() {
        await expect(this.eventNameInputLocator).toBeVisible();
    }
    async verifyEventNameInputNotEmpty() {
        await expect(await this.eventNameInputLocator.inputValue()).not.toBe('');
    }

    async clickPIDInput() {
        await this.pidInputLocator.waitFor({ state: 'visible', timeout: 15000 });
        await this.pidInputLocator.click();
    }

    async fillPID(PID: string) {
        await this.pidInputLocator.fill(PID);
    }

    async clickFetchEventDetails() {
        await this.fetchEventDetailsButtonLocator.waitFor({ state: 'visible', timeout: 10000 });
        await this.fetchEventDetailsButtonLocator.click();
    }

    async selectStoreGroups(selectableStoreGroups: string) {
        await this.selectionDropdownButtonLocator.click();
        await this.page.waitForTimeout(1000);
        for (const store of selectableStoreGroups.split(',')) {
            const label = this.storeGroupItemLocator.filter({ hasText: store.trim() });
            await label.locator('div[role="checkbox"], div[class*="border-2"]').click();
            await this.page.waitForTimeout(500);
        }
    }

    async verifyStoreGroupsSelected(expectedStores: string) {
        // Only open dropdown if not already open
        const isDropdownOpen = await this.storeGroupItemLocator.first().isVisible().catch(() => false);
        if (!isDropdownOpen) {
            await this.selectionDropdownButtonLocator.click();
            await this.page.waitForTimeout(1000);
        }
        for (const store of expectedStores.split(',')) {
            const label = this.storeGroupItemLocator.filter({ hasText: store.trim() });
            const checkmarkSvg = label.locator('svg.lucide-check');
            const isVisible = await checkmarkSvg.isVisible();
            if (!isVisible) {
                throw new Error(`Store group "${store}" not selected`);
            }
            console.log(`Checkbox for "${store}" is checked: ${isVisible}`);
        }
    }

    async verifyNoWarningMessageDisplayed() {
        await expect(this.pidErrorLocator).toHaveCount(0, { timeout: 5000 });
    }
}
