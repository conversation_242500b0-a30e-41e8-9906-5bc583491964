import { expect, Page } from "playwright/test";
import { ProductGroupComponent } from "../../../Components/baseComponent/EventFields/ProductGroup/ProductGroupComponent";
import { DivisionSelectionComponent } from "../../../Components/baseComponent/EventFields/Division/DivisionSelectionComponent";
import { StoreGroupComponent } from "../../../Components/baseComponent/EventFields/StoreGroup/StoreGroupComponent";
import { VehicleComponent } from "../../../Components/baseComponent/EventFields/Vehicle/VehicleComponent";


export class DivisionPromotionCreationPage {
    private divisionComponent: DivisionSelectionComponent;
    private productGroupComponent: ProductGroupComponent;
    private storeGroupComponent: StoreGroupComponent;
    private vehicleComponent: VehicleComponent;
    private page: Page;

    constructor(pageWithComponents: any) {
        this.page = pageWithComponents.page; // Store the actual Playwright Page object
        this.divisionComponent = pageWithComponents.divisionComponent;
        this.productGroupComponent = pageWithComponents.productGroupComponent;
        this.storeGroupComponent = pageWithComponents.storeGroupComponent;
        this.vehicleComponent = pageWithComponents.vehicleComponent;
    }


    async divisionSelection(divisionData: any) {
        console.log(`Verifying pre-populated division: ${divisionData?.name}`);
        await this.divisionComponent.selectDivision(divisionData);
    }

    async populateProductGroupDivisionPromotion(ppgData: any) {
        await this.productGroupComponent.selectProductGroup(ppgData);
    }

    async ViewPPGStores(ViewPPGStoresData: any) {
        await this.productGroupComponent.viewPPGStores(ViewPPGStoresData);
    }

    async getStoreGroupType(storeGroupTypeData: any) {
        await this.storeGroupComponent.getStoreGroupType(storeGroupTypeData);
    }

    async getStoreGroups(storeGroupsData: any) {
        await this.storeGroupComponent.getStoreGroups(storeGroupsData);
    }

    async vehicleTypeOrCustomDate(option: 'Other' | 'Promo Cycle' | 'Custom Date') {
        await this.vehicleComponent.selectVehicleType(option);
    }

    async yearSelection(yearData: any) {
        await this.vehicleComponent.selectYear(yearData);
    }

    async startWeekOrVehicle(week: string) {
        await this.vehicleComponent.selectStartWeek(week);
    }

    async prepopulateVehicleStartDate(date: string) {
        await this.vehicleComponent.verifyStartDate(date);
    }

    async prepopulateVehicleEndDate(date: string) {
        await this.vehicleComponent.verifyEndDate(date);
    }

    async saveEventDetails() {
        const saveButton = this.page.getByRole('button', { name: 'Save Event Details & Add' });
        await saveButton.waitFor({ state: 'visible', timeout: 30000 });
        await saveButton.click();
        await this.page.waitForLoadState("networkidle");
    }
}