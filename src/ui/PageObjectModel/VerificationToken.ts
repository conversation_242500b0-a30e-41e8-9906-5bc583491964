
import fs from 'fs/promises';
import path from 'path';
import { google } from 'googleapis';
import { authenticate } from '@google-cloud/local-auth';
import * as readline from 'readline';
import { promisify } from 'util';

const dirName = path.join(__dirname, 'keheVendor');
const SCOPES = ['https://www.googleapis.com/auth/gmail.readonly'];
const CREDENTIALS_PATH = path.join(dirName, 'credentials.json');
const TOKEN_PATH = path.join(dirName, 'token.json');

// Check and return saved credentials
async function loadSavedCredentialsIfExist() {
  try {
    const content = await fs.readFile(TOKEN_PATH, 'utf-8');
    const credentials = JSON.parse(content);
    return google.auth.fromJSON(credentials);
  } catch {
    return null;
  }
}

// Save token to disk
async function saveCredentials(client: any) {
  const content = await fs.readFile(CREDENTIALS_PATH, 'utf-8');
  const keys = JSON.parse(content);
  const key = keys.installed || keys.web;

  const payload = {
    type: 'authorized_user',
    client_id: key.client_id,
    client_secret: key.client_secret,
    refresh_token: client.credentials.refresh_token,
  };

  await fs.writeFile(TOKEN_PATH, JSON.stringify(payload, null, 2));
}

// Main function to authorize and generate token.json
export async function authorize() {
  let client = await loadSavedCredentialsIfExist();
  if (client) {
    console.log('✅ Token loaded from file');
    return client;
  }

  client = await authenticate({
    scopes: SCOPES,
    keyfilePath: CREDENTIALS_PATH,
  }) as any;

  if (client && client.credentials) {
    await saveCredentials(client);
    console.log('✅ Token saved to', TOKEN_PATH);
  }

  return Promise.resolve(client);
}

// Run directly if invoked as script
if (require.main === module) {
  authorize().catch(console.error);
}



async function getNewToken(oAuth2Client: any) {
  const authUrl = oAuth2Client.generateAuthUrl({
    access_type: 'offline',
    scope: SCOPES,
  });
  console.log('Authorize this app by visiting this URL:\n', authUrl);

  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  const question = promisify(rl.question).bind(rl);
  const code = await question('Enter the code from that page here: ');
  rl.close();

  try {
    const { tokens } = await oAuth2Client.getToken(code); // ← EXCHANGE CODE FOR TOKEN
    oAuth2Client.setCredentials(tokens);
    fs.writeFile(TOKEN_PATH, JSON.stringify(tokens, null, 2)); // ← SAVE TOKEN
    console.log('✅ Token stored to', TOKEN_PATH);
    return oAuth2Client;
  } catch (err) {
    console.error('❌ Error retrieving access token', err);
    throw err;
  }
}
