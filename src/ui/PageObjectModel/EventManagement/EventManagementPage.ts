import { Page, expect, Locator } from "@playwright/test";
import { pageFixture } from "../../../../config/pageFixtures";

export class EventManagementPage {
  readonly page: Page;
  readonly eventIdLocator: Locator;
  readonly actionsLocator: Locator;
  readonly pidLocator: Locator;
  readonly updateEventDetailsButtonLocator: Locator;
  readonly planningTableLocator: Locator;
  readonly tableRowsLocator: Locator;
  readonly tableHeaderCellsLocator: Locator;
  readonly closeIconInSendToMerchantModalLocator: Locator;
  readonly cancelButtonLocator: Locator;
  readonly confirmCancelButtonLocator: Locator;
  readonly modalLocator: Locator;
  readonly sendBackWithCommentsTextFieldLocator: Locator;
  readonly sendBackWithCommentsButtonLocator: Locator;
  readonly backButtonToReturnToPromotionManagementPage: Locator;
  public static eventId: any;
  public static tableEventIds: any;
  public static tableEventNames: any;
  constructor(page: Page) {
    this.page = page;
    this.eventIdLocator = this.page.locator(
      '//div[@id="event-header"]/div/span/span[2]'
    );
    this.actionsLocator = this.page.locator(`//button[@id='abs-dropdown-btn']`);
    this.cancelButtonLocator = this.page.locator(
      "(//div[@id='abs-edit-and-remove-buttons-container1']/span)[2]"
    );
    this.pidLocator = this.page.getByRole("spinbutton");
    this.updateEventDetailsButtonLocator = this.page.getByRole("button", {
      name: "Update Event Details",
    });
    this.closeIconInSendToMerchantModalLocator = this.page
      .getByTestId("close-icon-div-id")
      .getByRole("img");
    this.planningTableLocator = this.page.locator(
      '//table[@id="planning-table"]'
    );
    this.tableRowsLocator = this.page.locator(
      '//table[@id="planning-table"]/tbody/tr'
    );
    this.tableHeaderCellsLocator = this.page.locator(
      '//table[@id="planning-table"]/thead/tr/th/div'
    );

    this.modalLocator = this.page.getByTestId("modal-id");
    this.confirmCancelButtonLocator = this.page.locator(
      '//div[@id="abs-common-modal-islast-offer-promotion-section"]//button[contains(text(), "Cancel Event")]'
    );
    this.sendBackWithCommentsTextFieldLocator = this.page.getByRole('textbox', { name: 'Creating a draft. Please add' });
    this.sendBackWithCommentsButtonLocator = this.page.getByRole('button', { name: 'Add Comment & Send To Vendor' });
    this.backButtonToReturnToPromotionManagementPage = this.page.getByRole('button', { name: 'BACK' });
  }
  async clickOnActionOption(actionsOption: string) {
    const optionLocator = this.page.getByText(actionsOption);
    await optionLocator.waitFor({ state: 'visible' });
    await optionLocator.click();
  }
  async fetchEventId(): Promise<void> {
    const eventIdElement = await this.eventIdLocator;
    if (!eventIdElement) {
      throw new Error("Event ID element not found");
    }
    const eventIdText: any = await eventIdElement.textContent();
    EventManagementPage.eventId = eventIdText.match(/\d+/)?.[0];
    if (!EventManagementPage.eventId) {
      throw new Error("Event ID not found");
    }
    console.log(eventIdText, "eventIdText");
  }
  async clickOnUpdateEventDetailsButton(twiceClick: boolean= true): Promise<void> {
    await this.updateEventDetailsButtonLocator.click();
    if(twiceClick) {
    await this.updateEventDetailsButtonLocator.click();
    }
  }

  async getEventId(): Promise<string> {
    return EventManagementPage.eventId;
  }

  async verifyEventStatus(status: string) {
    await this.page.waitForSelector("#event-header", { state: "visible" });
    const eventStatusFetchedAsNationalMerchant = await this.page
      .getByTestId("event-header")
      .getByText(status)
      .innerText();
    expect(eventStatusFetchedAsNationalMerchant).toContain(status);
  }

  async hoverOverActions() {
    await this.page.locator(`//button[@id='abs-dropdown-btn']`).hover();
  }
  
  async clickOverActions() {
    await this.actionsLocator.click();
  }

  async verifyStatusInBreakdownTable(expectedStatus: string): Promise<void> {
    try {
      // Find and scroll to Division Level Breakdown section
      const breakdownSection = this.page.getByText("Division Level Breakdown");
      await breakdownSection.waitFor({ state: "attached", timeout: 10000 });
      await breakdownSection.scrollIntoViewIfNeeded();
      await breakdownSection.waitFor({ state: "visible", timeout: 10000 });

      // Wait for any animations to complete after scrolling
      await this.page.waitForTimeout(1000);

      // Locate the breakdown table and wait for it to be stable
      const table = this.page.locator(
        "//table[@id='abs-child-events-view-table-0']"
      );
      await table.waitFor({ state: "visible", timeout: 15000 });
      await this.page.waitForLoadState("networkidle");
      await this.page.waitForTimeout(2000); // Give table time to fully render

      // Special handling for canceled status
      if (expectedStatus.toLowerCase() === "canceled" || expectedStatus.toLowerCase() === "rejected") {
        await this.page.waitForTimeout(3000); // Extra wait for canceled status to appear

        // Ensure table is scrolled into view
        await table.scrollIntoViewIfNeeded();
        await this.page.waitForTimeout(1000); // Wait for scroll to settle

        const statusElements = await table
          .locator(
            "//table[@id='abs-child-events-view-table-0']/tbody/tr/td[3]"
          )
          .all();
        console.log(`Found ${statusElements.length} status elements`);

        for (const statusElement of statusElements) {
          try {
            const row = await statusElement.locator("../../.."); // Go up to tr
            const divisionText = await row.locator("td").first().innerText();

            // Skip Shaw's division
            if (divisionText.toLowerCase().includes("shaws")) {
              console.log(`Skipping status check for Shaw's division`);
              continue;
            }

            const statusText = await statusElement.innerText();
            console.log(
              `Checking division "${divisionText}" - Found status: "${statusText}"`
            );

            if (
              statusText.trim().toLowerCase() === expectedStatus.toLowerCase()
            ) {
              console.log(
                `✓ Found canceled status for division "${divisionText}"`
              );
            } else {
              await row.screenshot({
                path: `row-status-error-${divisionText}.png`,
              });
              throw new Error(
                `Division "${divisionText}" has incorrect status. Expected "${expectedStatus}", found "${statusText}"`
              );
            }
          } catch (error) {
            console.error("Error checking status:", error);
            throw error;
          }
        }
        console.log("✓ All divisions show canceled status");
        return;
      }

      // For other statuses, use the original implementation with status column
      const headerCells = await table
        .locator("tr:first-child td, tr:first-child th")
        .allTextContents();
      const statusColIdx = headerCells.findIndex(
        (h) => h.trim().toLowerCase() === "status"
      );
      if (statusColIdx === -1) {
        throw new Error(
          `Status column not found in table headers: ${headerCells.join(", ")}`
        );
      }

      // Get all data rows (excluding header)
      const rows = await table.locator("tr").elementHandles();
      for (let i = 1; i < rows.length; i++) {
        const cells = await rows[i].$$("td, th");
        if (cells.length > statusColIdx) {
          const actualStatus = (await cells[statusColIdx].innerText()).trim();
          if (
            actualStatus.toLowerCase() !== expectedStatus.trim().toLowerCase()
          ) {
            await rows[i].screenshot({ path: `row-${i}-status-error.png` });
            throw new Error(
              `Status mismatch in row ${i}: Expected "${expectedStatus}", found "${actualStatus}"`
            );
          }
        }
      }
      console.log(`✓ All divisions show status: ${expectedStatus}`);
    } catch (error) {
      console.error("Error verifying table status:", error);
      await this.page.screenshot({ path: "status-verification-error.png" });
      throw error;
    }
  }

  async verifyDivisionLevelBreakdownVisible(): Promise<void> {
    try {
      const divisionBreakdown = this.page.locator(
        "text=Division Level Breakdown"
      );
      await divisionBreakdown.waitFor({ state: "visible", timeout: 10000 });
      await expect(divisionBreakdown).toBeVisible();
      console.log("✓ Division Level Breakdown section is visible");
    } catch (error) {
      console.error(
        "Error verifying Division Level Breakdown visibility:",
        error
      );
      throw new Error("Division Level Breakdown section is not visible");
    }
  }
  async VerifySendToMerchantButtonEnabledAndClose(
    buttonLabel: string
  ): Promise<void> {
    const button = this.page.getByRole("button", { name: buttonLabel });
    await expect(button).toBeEnabled();
    await button.click();
    await this.closeIconInSendToMerchantModalLocator.click();
  }

  async verifyUpdatedPidIsVisible(updatedPID: string): Promise<void> {
    const pidLink = this.page.getByRole("link", { name: updatedPID });
    await expect(pidLink).toBeVisible();
  }
  async updatePid(pid: string): Promise<void> {
    await this.pidLocator.click();
    await this.pidLocator.fill(pid);
  }
  async verifySendToVendorButtonIsDisabled(buttonLabel: string): Promise<void> {
    await this.actionsLocator.click();
    await this.page.waitForTimeout(1000);

    const button = this.page.getByRole("button", {
      name: new RegExp(buttonLabel, "i"),
    });

    const isActuallyDisabled = await button.evaluate(
      (el) =>
        el.hasAttribute("disabled") ||
        el.getAttribute("aria-disabled") === "true" ||
        window.getComputedStyle(el).pointerEvents === "none"
    );

    expect(isActuallyDisabled).toBe(true);
  }
  async clickOnEditEventDetailsButton(label: string): Promise<void> {
    const page = pageFixture.page;
    await page.getByText(label, { exact: true }).click();
    await this.page.waitForLoadState("networkidle");
  }
  async verifyEventIdDisplayed(): Promise<void> {
    try {
      // Wait for event header to be visible
      await this.page.waitForSelector("#event-header", { state: "visible" });

      // Get displayed event ID
      const displayedEventId = await this.eventIdLocator.textContent();
      const displayedNumber = displayedEventId?.match(/\d+/)?.[0];

      // Get stored event ID using getter method
      const storedEventId = await this.getEventId();

      // Compare with stored event ID
      if (!displayedNumber || !storedEventId) {
        throw new Error(
          "Could not verify event ID - missing displayed ID or stored parent ID"
        );
      }

      // Verify the IDs match
      await expect(displayedNumber).toBe(storedEventId);
      console.log("✓ Parent Event ID verified successfully");
    } catch (error) {
      console.error("Error verifying event ID:", error);
      throw error;
    }
  }

  async clickOnChildEventDraft(): Promise<void> {
    try {
      // Click on Draft first
      await this.page.getByText("Draft", { exact: true }).first().click();
      await this.page.waitForTimeout(1000); // Brief wait for UI update
      console.log("✓ Successfully clicked on child event draft");
    } catch (error) {
      console.error("Error clicking on child event draft:", error);
      throw error;
    }
  }

  async clickSendBackWithCommentsTextField() {
    await this.sendBackWithCommentsTextFieldLocator.click();
  }

  async enterCommentsInSendBackWithCommentsTextField(comments: string) {
    await this.sendBackWithCommentsTextFieldLocator.fill(comments);
  }

  async clickAddCommentAndSendToVendorButton() {
    await this.sendBackWithCommentsButtonLocator.click();
    await this.page.waitForLoadState('networkidle');
  }

  async clickBackButtonToReturnToPromotionManagementPage() {
    await this.backButtonToReturnToPromotionManagementPage.click();
  }

  async verifyActionOptionEnabled(actionsOption: string): Promise<void> {
    const actionOptionLocator = this.page.locator("(//div[@id='abs-dropdown-btn']//ul//li)[2]");
    const optionText = await actionOptionLocator.textContent();
    expect(optionText?.trim()).toBe(actionsOption);
    await actionOptionLocator.waitFor({ state: 'visible' });
    const isEnabled = await actionOptionLocator.isEnabled();
    expect(isEnabled).toBe(true);
  }
}
