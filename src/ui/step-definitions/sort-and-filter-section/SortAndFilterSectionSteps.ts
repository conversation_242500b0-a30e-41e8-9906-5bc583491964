import { When } from "@cucumber/cucumber";
import { pageFixture } from "../../../../config/pageFixtures";
import { SortAndFilterSectionPage } from "../../PageObjectModel/SortAndFilerSection/SortAndFilterSectionPage";

When("User clicks 'Sort & Filter' icon", async function () {
  const sortAndFilterSectionPage = new SortAndFilterSectionPage(
    pageFixture.page
  );
  await sortAndFilterSectionPage.clickSortAndFilterIcon();
});

When("User expands the 'Division' Filter", async function () {
  const sortAndFilterSectionPage = new SortAndFilterSectionPage(
    pageFixture.page
  );
  await sortAndFilterSectionPage.expandDivisionFilter();
});

When("User selects division number {string}", async function (divisionNumber) {
  const sortAndFilterSectionPage = new SortAndFilterSectionPage(
    pageFixture.page
  );
  await sortAndFilterSectionPage.selectDivision(divisionNumber);
});

When("User expands the 'Event Type' Filter", async function () {
  const sortAndFilterSectionPage = new SortAndFilterSectionPage(
    pageFixture.page
  );
  await sortAndFilterSectionPage.expandEventTypeFilter();
});

When(
  "User selects the event type {string} in the 'Sort & Filter' section",
  async function (eventType) {
    const sortAndFilterSectionPage = new SortAndFilterSectionPage(
      pageFixture.page
    );
    await sortAndFilterSectionPage.selectEventType(eventType);
  }
);

When(
  "User clicks the 'Apply' button in the 'Sort & Filter' section",
  async function () {
    const sortAndFilterSectionPage = new SortAndFilterSectionPage(
      pageFixture.page
    );
    await sortAndFilterSectionPage.clickApplyButton();
  }
);

When("User expands the 'Group and Categories' filter", async function () {
  const sortAndFilterSectionPage = new SortAndFilterSectionPage(
    pageFixture.page
  );
  await sortAndFilterSectionPage.expandGroupAndCategoriesFilter();
});

When(
  "User selects all the checkboxes under 'Group and Categories'",
  async function () {
    const sortAndFilterSectionPage = new SortAndFilterSectionPage(
      pageFixture.page
    );
    await sortAndFilterSectionPage.selectAllGroupAndCategoriesCheckboxes();
  }
);

When("User selects all checkboxes under eventType in Sort & Filter", async function () {
  const sortAndFilterSectionPage = new SortAndFilterSectionPage(
    pageFixture.page
  );
  await sortAndFilterSectionPage.selectAllEventTypeCheckboxesInSortAndFilter();
});

When("User selects all the checkboxes under 'Vehicle Type'", async () => {
  const sortAndFilterSectionPage = new SortAndFilterSectionPage(
    pageFixture.page
  );
  await sortAndFilterSectionPage.selectAllVehicleTypeCheckboxes();
});

When(
  "User clicks the close icon of the 'Sort & Filter' section",
  async function () {
    const sortAndFilterSectionPage = new SortAndFilterSectionPage(
      pageFixture.page
    );
    await sortAndFilterSectionPage.clickCloseIcon();
  }
);

When(`User selects vehicle type as {string} in sort and filter`, async (vehicleType) => {
  const sortAndFilterSectionPage = new SortAndFilterSectionPage(
    pageFixture.page
  );
  await sortAndFilterSectionPage.selectingVehicleTypeInSortAndFilter(vehicleType);
});

When('User expands vehicleType in sort and filter', async () => {
  const sortAndFilterSectionPage = new SortAndFilterSectionPage(
    pageFixture.page
  );
  await sortAndFilterSectionPage.openVehicleTypeInSortAndFilter();
});



When('User expands Allowance Type in sort and filter', async () => {
  const sortAndFilterSectionPage = new SortAndFilterSectionPage(
    pageFixture.page
  );
  await sortAndFilterSectionPage.openAllowanceTypeInSortAndFilter();
});

When('User selects Allowance type as {string} in sort and filter', async (allowanceType) => {
  const sortAndFilterSectionPage = new SortAndFilterSectionPage(
    pageFixture.page
  );
  await sortAndFilterSectionPage.selectingAllowanceTypeInSortAndFilter(allowanceType);
});


When('User expands Performance in sort and filter', async () => {
  const sortAndFilterSectionPage = new SortAndFilterSectionPage(
    pageFixture.page
  );
  await sortAndFilterSectionPage.openPerformanceInSortAndFilter();
});

When('User selects performance as {string} in sort and filter', async (performance) => {
  const sortAndFilterSectionPage = new SortAndFilterSectionPage(
    pageFixture.page
  );
  await sortAndFilterSectionPage.selectingPerformanceInSortAndFilter(performance);
});

When('User expands promo type in sort and filter', async () => {
  const sortAndFilterSectionPage = new SortAndFilterSectionPage(
    pageFixture.page
  );
  await sortAndFilterSectionPage.openPromoTypeInSortAndFilter();
});

When('User selects promo type as {string} in sort and filter', async (promoType) => {
  const sortAndFilterSectionPage = new SortAndFilterSectionPage(
    pageFixture.page
  );
  await sortAndFilterSectionPage.selectingPromoTypeInSortAndFilter(promoType);
});