import { When, Then } from "@cucumber/cucumber";
import { ContextHandling } from "../../../src/ui/Components/baseComponent/ContextHandling";
import { pageFixture } from "../../../config/pageFixtures";
import { PromotionManagementPage } from "../../../src/ui/PageObjectModel/PromotionManagement/PromotionManagementPage";
import { EventManagementPage } from "../PageObjectModel/EventManagement/EventManagementPage";
import { expect } from "@playwright/test";

let promotionManagementPage: PromotionManagementPage;

When("User closes the current browser context", async () => {
  const contextHandling = new ContextHandling(pageFixture.page);
  await contextHandling.handleContextSwitching(pageFixture.page.context());
});
When(
  "User Gathers EventIds List From Promotion Management Table",
  async function () {
    const promotionManagementPage = new PromotionManagementPage(
      pageFixture.page
    );
    await promotionManagementPage.getEventIdsFromTable();
  }
);

When("User opens a new browser context", async () => {
  const contextHandling = new ContextHandling(pageFixture.page);
  await contextHandling.openNewBrowserContext(pageFixture.page.context());
});

When("User clicks the view dropdown", async () => {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.clickViewDropdown();
});

When(
  "User selects {string} from the view dropdown",
  async (viewDropdownOption: string) => {
    const promotionManagementPage = new PromotionManagementPage(
      pageFixture.page
    );
    await promotionManagementPage.selectViewDropdownOption(viewDropdownOption);
  }
);

When(
  "User clicks the default-selected 'Offer ID#' search criteria option dropdown",
  async () => {
    const promotionManagementPage = new PromotionManagementPage(
      pageFixture.page
    );
    await promotionManagementPage.clickOfferIdSearchCriteriaDropdown();
  }
);

When(
  "User selects {string} from the search criteria option dropdown",
  async (searchCriteriaOption: string) => {
    const promotionManagementPage = new PromotionManagementPage(
      pageFixture.page
    );
    await promotionManagementPage.selectSearchCriteriaOption(
      searchCriteriaOption
    );
  }
);

When(
  "User enters the event ID or eventName {string} in the search criteria input field",
  async (searchCriteriaOption: string) => {
    const promotionManagementPage = new PromotionManagementPage(
      pageFixture.page
    );
    if(searchCriteriaOption=="EventID#") {
    await promotionManagementPage.enterEventIdInSearchCriteria();
  }
  else if(searchCriteriaOption=="Event Name") {
    await promotionManagementPage.enterEventNameInSearchCriteria();
  }}
);
When(
  "User Gathers EventNames List From Promotion Management Table",
  async function () {
    const promotionManagementPage = new PromotionManagementPage(
      pageFixture.page
    );
    await promotionManagementPage.getEventNamesFromTable();
  })
When("User clicks the 'Search' icon", async () => {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.clickSearchIcon();
});

When(
  "User clicks the National Event's record displayed as result of search in the search result section",
  async () => {
    const promotionManagementPage = new PromotionManagementPage(
      pageFixture.page
    );
    await promotionManagementPage.clickNationalEventRecordInSearchResult();
  }
);

When("User verifies the display of the event in the search result", async () => {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.verifyEventInTable();
});

When("User clicks on 'View' dropdown", async function () {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.clickViewDropdown();
});

When("User opens the preference menu", async function () {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.clicksPrefEllipses();
});

When("User selects 'Update Preference'", async function () {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.selectsUpdatePreference();
});

When("User selects save as new in preference", async function () {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.selectsSaveAsNewPref();
});

When("User set as default preference", async function () {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.selectSetAsDefaultPref();
});

When("User enters preference name", async function () {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.entersNewPrefName();
});

Then("User saves the new preference", async function () {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.savePref();
});

When("User clicks on preference dropdown", async function () {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.clickPreferenceDropDown();
});

When(
  "User selects test preference from preference dropdown",
  async function () {
    const promotionManagementPage = new PromotionManagementPage(
      pageFixture.page
    );
    await promotionManagementPage.selectExistingPreference();
  }
);

When("User selects 'delete selected' preference", async function () {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.deleteSelectedPreference();
});

When("User deletes the saved preference", async function () {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.deleteSelectedPreference();
});

When("User opens a new browser session", async function () {
  const contextHandling = new ContextHandling(pageFixture.page);
  await contextHandling.openNewBrowserContext(pageFixture.page.context());
});

Then(
  "Parent Event ID should be displayed at the top of the child event page for visibility",
  async function () {
    const eventManagementPage = new EventManagementPage(pageFixture.page);
    await eventManagementPage.verifyEventIdDisplayed();
  }
);

When("User clicks on the child event", async function () {
  const eventManagementPage = new EventManagementPage(pageFixture.page);
  await eventManagementPage.clickOnChildEventDraft();
});
When("User selects eventStatus dropdown in planning view", async () => {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.selectingEventStatusDropdownInPlanningView();
});

When("User clicks on selectAll in eventStatus dropdown", async () => {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.togglingEventStatusSelectUnselectAll();
});
When("User selects event status as Draft in eventStatus dropdown", async () => {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.selectingEventStatusasDraft();
});
When("User clicks the Filter By Type dropdown in Tasks View", async () => {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.clickOnFilterByTypeInTasksView();
});
When(
  "User selects all items from the Filter By Type dropdown in Tasks View",
  async () => {
    const promotionManagementPage = new PromotionManagementPage(
      pageFixture.page
    );
    await promotionManagementPage.selectsAllInFilterByTypeDropdownInTasksView();
  }
);
When(
  "User selects {string} from Event type in Sort & Filter",
  async (eventType: string) => {
    const promotionManagementPage = new PromotionManagementPage(
      pageFixture.page
    );
    await promotionManagementPage.selectingEventType(eventType);
  }
);

When("User saves a new preference", async () => {
  promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.saveNewPreferenceFlow(false);
});

When("User saves new preference as default", async () => {
  promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.saveNewPreferenceFlow(true);
});
Then('User verifies the display of {string} in the search result section', async (expectedMessage: string) => {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.verifyNoRecordsFound(expectedMessage);
});
When("User selects the saved preference", async () => {
  await promotionManagementPage.selectingSavedPreference();
});

When("Deleting previous default preference", async () => {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.deletingPreviousDefaultPreference();
});

When(`User deletes the selected preference`, async () => {
  await promotionManagementPage.deletePreference(false);
});

When(
  "User selects Event Status as {string} in Allowance View",
  async (status) => {
    const promotionManagementPage = new PromotionManagementPage(
      pageFixture.page
    );
    await promotionManagementPage.selectingEventStatusinAllowanceView(status);
  }
);

When("User clicks on Status dropdown in Allowance View", async () => {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.openingStatusDropdowninAllowanceView();
});

When("User clicks on selectorUnselectAll in the dropdown", async () => {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.selectOrUnselectAll();
});

When("User clicks on Allowance Type dropdown in Allowance View", async () => {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.openAllowanceTypeDropdowninAllowanceView();
});

When("User clicks on Apply button", async () => {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.clickApplyButtoninDropdowns();
});

When(
  "User selects Allowance Type as {string} in Allowance View",
  async (allowanceType) => {
    const promotionManagementPage = new PromotionManagementPage(
      pageFixture.page
    );
    await promotionManagementPage.selectingAllowanceTypeinAllowanceView(
      allowanceType
    );
  }
);

When("User clicks on Performance Type dropdown in Allowance View", async () => {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.openingPerformanceTypeDropdowninAllowanceView();
});

When(
  "User selects Performace Type as {string} in Allowance View",
  async (performanceType) => {
    const promotionManagementPage = new PromotionManagementPage(
      pageFixture.page
    );
    await promotionManagementPage.selectingPerformaceTypeinAllowanceView(
      performanceType
    );
  }
);

When("User clicks on Date Type dropdown in Allowance View", async () => {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.openingDateTypeDropdowninAllowanceView();
});

When("User selects Date Type in Allowance View", async () => {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.selectingDateTypesinAllowanceView(
    "Date Type: Event Dates"
  );
});

When("User clicks on Vehicle Type dropdown in Allowance View", async () => {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.openingVehicleTypeDropdowninAllowanceView();
});

When(
  "User selects Vehicle Type as {string} in Allowance View",
  async (vehicleType) => {
    const promotionManagementPage = new PromotionManagementPage(
      pageFixture.page
    );
    await promotionManagementPage.selectingVehicleTypeinAllowanceView(
      vehicleType
    );
  }
);

When("User clicks on filter types dropdown in Alerts view", async () => {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.clickFilterTypesDropdownInAlertsView();
});

When("User selects 'Event Published' in filter types dropdown of Alert view", async () => {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.selectEventPublishedOptionInEventStatusDropdownInAlertsView();
});

When("User clicks on filter types dropdown", async () => {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.clickFilterTypesDropdown();
});

When("User selects or deselects all filter types in the dropdown", async () => {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.selectAllFilterTypesDropdown();
});

When("User clicks on 'Apply' button in the filter types dropdown", async () => {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.clickApplyButtonInFilterTypesDropdown();
});

When("User clicks on menu icon of preferences", async () => {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.openPreferencesMenu();
});

When("User clicks on Save As New Preference Button", async () => {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.clickSaveAsNewPreference();
});

When("User clicks on Update Preference Button", async () => {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.clickUpdatePreferenceButton();
});

When("User clicks on save button", async () => {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.clickpreferenceSaveButton();
});

When("User selects 'New Item' in filter types dropdown of Alert view", async () => {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.selectNewItemFilterTypeInAlertView();
});

When("User selects 'New Item' in filter types dropdown of Tasks view", async () => {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.selectNewItemFilterTypeInTasksView();
});


When("User clicks on sort dropdown", async () => {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.selectSortDropdown();
});

When('User selects {string} option from sort dropdown', async function(sortOption: string) {
    const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
    await promotionManagementPage.selectVendorEventStartSortOptionFromDropdown();
});

When("User enters search text {string} in search box", async (searchText: string) => {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.searchTextboxLocator.click();
  await promotionManagementPage.searchTextboxLocator.fill(searchText);
});

When("User clicks on first search result link", async () => {
  // Click the first search result link in the search results
  await pageFixture.page.locator('a').first().click();
});

When("User clicks BACK button on the event details page", async () => {
  await pageFixture.page.getByRole('button', { name: 'BACK' }).click();
});

When("User clicks on search box", async () => {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.searchTextboxLocator.click();
});

Then("User verifies search value {string} persists in search box", async (expectedValue: string) => {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  const actualValue = await promotionManagementPage.searchTextboxLocator.inputValue();
  expect(actualValue).toBe(expectedValue);
});

When("User clicks the search icon", async () => {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.clickSearchIcon();
});

Then("User should see error message for invalid input", async () => {
  // Check for error message using the specific test ID
  await expect(pageFixture.page.getByTestId('abs-api-errors7')).toBeVisible();
});

When("User enters vendor tracking number {string}", async (trackingNumber: string) => {
  await pageFixture.page.locator('input[name="vendorOfferTrackingNbr"]').fill(trackingNumber);
});
