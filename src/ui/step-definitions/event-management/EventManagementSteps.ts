import { Given, When, Then } from "@cucumber/cucumber";
import { pageFixture } from "../../../../config/pageFixtures";
import { EventManagementPage } from "../../PageObjectModel/EventManagement/EventManagementPage";
import { PromotionManagementPage } from "../../PageObjectModel/PromotionManagement/PromotionManagementPage";
import { expect } from "@playwright/test";

When(
  "User clicks on the {string} option under 'Actions'",
  async function (actionsOption: string) {
    const eventManagementPage = new EventManagementPage(pageFixture.page);
    await eventManagementPage.clickOnActionOption(actionsOption);
  }
);

When("User fetches the event ID after the event is created", async function () {
  const eventManagementPage = new EventManagementPage(pageFixture.page);
  await eventManagementPage.fetchEventId();
});
Then(
  "User verifies whether the event status is {string}",
  async function (status: string) {
    const eventManagementPage = new EventManagementPage(pageFixture.page);
    await eventManagementPage.verifyEventStatus(status);
  }
);

When("User hovers over 'Actions'", async function () {
  const eventManagementPage = new EventManagementPage(pageFixture.page);
  await eventManagementPage.hoverOverActions();
});

When("User clicks over 'Actions'", async function () {
  const eventManagementPage = new EventManagementPage(pageFixture.page);
  await eventManagementPage.clickOverActions();
});

Then(`User verifies if {string} is enabled in the 'Actions' dropdown`, async (arg: string) => {
  const eventManagementPage = new EventManagementPage(pageFixture.page);
  await eventManagementPage.verifyActionOptionEnabled(arg);
});

When("User updates the PID to {string}", async function (pid: string) {
  const eventManagementPage = new EventManagementPage(pageFixture.page);
  await eventManagementPage.updatePid(pid);
});
Then(
  "The event should be saved and updated with the new PID {string}",
  async (updatedPID: string) => {
    const eventManagementPage = new EventManagementPage(pageFixture.page);
    await eventManagementPage.verifyUpdatedPidIsVisible(updatedPID);
  }
);
Then("The {string} button should be enabled in Actions dropdown", async (buttonLabel: string) => {
  const eventManagementPage = new EventManagementPage(pageFixture.page);
  await eventManagementPage.VerifySendToMerchantButtonEnabledAndClose(
    buttonLabel
  );
});
Then("The {string} button should be disabled in Actions dropdown", async (buttonLabel: string) => {
  const eventManagementPage = new EventManagementPage(pageFixture.page);
  await eventManagementPage.verifySendToVendorButtonIsDisabled(buttonLabel);
});
When("User clicks on Event status dropdown in Planning view", async () => {
  const promotionManagementPage = new PromotionManagementPage(pageFixture.page);
  await promotionManagementPage.clickonEventStatusDropdownInPlanningView();
});
When(
  "User selects and Deselects All from Event Status dropdown In Planning View",
  async () => {
    const promotionManagementPage = new PromotionManagementPage(
      pageFixture.page
    );
    await promotionManagementPage.togglingEventStatusSelectUnselectAll();
  }
);
When(
  "User selects {string} in the Event Status dropdown in Planning View",
  async function (statusType: string) {
    const promotionManagementPage = new PromotionManagementPage(
      pageFixture.page
    );
    await promotionManagementPage.selectEventStatusInPlanningView(statusType);
  }
);
Given(
  "User clicks on the Update Event Details button to update the event information",
  async () => {
    const eventManagementPage = new EventManagementPage(pageFixture.page);
    await eventManagementPage.clickOnUpdateEventDetailsButton();
  }
);
Given(
  "User clicks on the Update Event Details button to update the event information once",
  async () => {
    const eventManagementPage = new EventManagementPage(pageFixture.page);
    await eventManagementPage.clickOnUpdateEventDetailsButton(false);
  }
);
Given(
  "User clicks on the {string} button to update the event information",
  async (label: string) => {
    const eventManagementPage = new EventManagementPage(pageFixture.page);
    await eventManagementPage.clickOnEditEventDetailsButton(label);
  }
);

Then(`User will verify Division Level Breakdown is visible`, async function () {
  const eventManagementPage = new EventManagementPage(pageFixture.page);
  await eventManagementPage.verifyDivisionLevelBreakdownVisible();
});

Then(
  "User verifies the event status in breakdown table is {string}",
  async function (expectedStatus: string) {
    const eventManagementPage = new EventManagementPage(pageFixture.page);
    await eventManagementPage.verifyStatusInBreakdownTable(expectedStatus);
  }
);

Then(
  "User verifies whether all the divisional events in the 'Event Details' tab of the 'Division Level Breakdown' table have the status as {string}",
  async function (expectedStatus: string) {
    const eventManagementPage = new EventManagementPage(pageFixture.page);
    await eventManagementPage.verifyStatusInBreakdownTable(expectedStatus);
  }
);

Then("User clicks on Send back with comments textField", async ()=> {
  const eventManagementPage = new EventManagementPage(pageFixture.page);
  await eventManagementPage.clickSendBackWithCommentsTextField();
});

Then("User clicks on the Add Comment & Send To Vendor button", async ()=> {
  const eventManagementPage = new EventManagementPage(pageFixture.page);
  await eventManagementPage.clickAddCommentAndSendToVendorButton();
});

Then("User clicks on back button to return to promotion management page", async ()=> {
  const eventManagementPage = new EventManagementPage(pageFixture.page);
  await eventManagementPage.clickBackButtonToReturnToPromotionManagementPage();
});
