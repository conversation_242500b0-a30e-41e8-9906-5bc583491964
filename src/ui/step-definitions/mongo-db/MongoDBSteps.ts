import { When } from '@cucumber/cucumber';
import {mongoDBComponent} from './mongoContext';


When("User connects to the MongoDB instance of a specific environment", async function () {
    await mongoDBComponent.connect();
});

When("User gets a reference to the {string} collection", async function (collectionName: string) {
    await mongoDBComponent.getReferenceToDBCollection(collectionName);
});

When("User searches by {string} and {string} in the referenced collection for {string}", async function (attributeName: string, attributeValue: string, valueType: string) {
    await mongoDBComponent.searchByAttributeValue(attributeName, attributeValue, valueType);
});

When("User retrieves the values of the array field {string}", async function (arrayFieldName: string) {
    await mongoDBComponent.getArrayFieldValues(arrayFieldName);
});

When("User searches by {string} with each of the retrieved array field's value for {string} and stores the results in an array", async function (attributeName: string, valueType: string) {
    await mongoDBComponent.searchByAttributeValueWithArrayValues(attributeName, valueType);
});
