@UI-Regression
Feature: Verify the Lead selection for multivendor PPG
  @UI-18600
  Scenario Outline: Verify the Lead selection for multivendor PPG
  Given "National Vendor" logs into MEUPP application
  When User clicks on "Add event"
  And User select the event type "National"
  And User populates the product group with "<PPG>"
  And User selects vehicle type "<vehicleType>"
  And User sets event date as "<eventDate>"
  And User save the event details
  And User Remove Invalid divisions if exists
  And User save the event details
  Then Verify the user can save the event details successfully
  When User click on "<Allowance type>"
  And User selects the "<Performace type>"
  And User selects "Separate Allowances By DSD Distributor" radio button in the first stepper of the allowance workflow section
  And User clicks the checkbox preceding the text, 'I will submit Allowances for all Stores & Items, including those delivered by other Vendors and I will be funding them.'
  Then Verify "My Vendors" and "Other Vendors" headers are displayed in the dates stepper
  When User clicks on Enter amounts
  And User select the "Lead Distributor Only"
  And User click on the "Lead Distributor"
  Then Verify the Lead selection for multivendor PPG
  Examples:
  | vehicleType   | eventDate | PPG    | Allowance type | Performace type |
  | Weekly Insert | FUTURE~15 | 827420 | Scan           | 4U Event (52)   |
