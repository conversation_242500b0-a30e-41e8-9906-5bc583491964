@UI-Regression
Feature: Verify the display of vendor names for multivendor PPG in the allowance workflow's dates stepper
  @UI-18592
  Scenario Outline: As a National Vendor, for a National Event created using a multivendor PPG, user should be able to see the vendor names of the PPG in the allowance workflow's dates stepper
  Given "National Vendor" logs into MEUPP application
  When User clicks on "Add event"
  And User select the event type "National"
  And User populates the product group with "<ppgId>"
  And User selects vehicle type "<vehicleType>"
  And User sets event date as "<eventDate>"
  And User save the event details
  And User Remove Invalid divisions if exists
  And User save the event details
  Then Verify the user can save the event details successfully
  When User click on "<Allowance type>"
  And User selects the "<Performace type>"
  And User selects "Separate Allowances By DSD Distributor" radio button in the first stepper of the allowance workflow section
  And User clicks the checkbox preceding the text, 'I will submit Allowances for all Stores & Items, including those delivered by other Vendors and I will be funding them.'
  Then Verify "My Vendors" and "Other Vendors" headers are displayed in the dates stepper
  When User connects to the MongoDB instance of a specific environment
  And User gets a reference to the "<collectionNameOne>" collection
  And User searches by "<attributeNameOne>" and "<ppgId>" in the referenced collection for "<valueTypeOne>"
  And User retrieves the values of the array field "simsSubAccntVendors"
  And User gets a reference to the "<collectionNameTwo>" collection
  And User searches by "<attributeNameTwo>" with each of the retrieved array field's value for "<valueTypeTwo>" and stores the results in an array
  Then Verify whether the results stored in the array are displayed as the distributor list in the dates stepper
  Examples:
  | vehicleType   | eventDate | ppgId  | Allowance type | Performace type | collectionNameOne | attributeNameOne     | collectionNameTwo | valueTypeOne | attributeNameTwo | valueTypeTwo |
  | Weekly Insert | FUTURE~15 | 827420 | Scan           | 4U Event (52)   | planproductgroups | sourceProductGroupId | simsvendors       | number       | vendorNumber     | string       |