@UI-Regression
Feature: Verify the Grayed out field for National vendor.
Background:
    Given "National Vendor" logs into MEUPP application
    When  User clicks on "Add event"
    And User select the event type "National"
  @UI-18597
  Scenario Outline: Verify the Grayed out field for national vendor by passing multivendor ppg.
    And User populates the product group with "<ppgId>"
    And User selects vehicle type "<vehicleType>"
    And User sets event date as "<eventDate>"
    And User save the event details
    And User Remove Invalid divisions if exists
    And User save the event details
    Then Verify the user can save the event details successfully
    When User click on "<Allowance type>"
    And User selects the "<Performace type>"
    And User selects "Separate Allowances By DSD Distributor" radio button in the first stepper of the allowance workflow section
    And User clicks the checkbox preceding the text, 'I will submit Allowances for all Stores & Items, including those delivered by other Vendors and I will be funding them.'
    Then Verify "My Vendors" and "Other Vendors" headers are displayed in the dates stepper
    When User will click on Edit Divison Dates
    And User connects to the MongoDB instance of a specific environment
    And User gets a reference to the "<collectionNameOne>" collection
    And User searches by "<attributeNameOne>" and "<ppgId>" in the referenced collection for "<valueTypeOne>"
    And User retrieves the values of the array field "simsSubAccntVendors"
    And User gets a reference to the "<collectionNameTwo>" collection
    And User searches by "<attributeNameTwo>" with each of the retrieved array field's value for "<valueTypeTwo>" and stores the results in an array
    Then Verify whether the results stored in the array are displayed as the distributor list in the dates stepper
    When User clicks on Enter amounts
    Then Verify the grayed out field for multivendor PPG
    Examples:
    | vehicleType   | eventDate     | Allowance type | Performace type |collectionNameOne | attributeNameOne     | collectionNameTwo | valueTypeOne | attributeNameTwo | valueTypeTwo |ppgId |
    | Weekly Insert | FUTURE~15     | Scan           | 4U Event (52)   | planproductgroups| sourceProductGroupId | simsvendors       | number       | vendorNumber     | string       |827420|

 @UI-18598
 Scenario Outline: Verify the National vendor should not see List cose , Master Cost in amount screen by passing multivendor ppg.
    Then User populates the product group with "<ppgId>"
    When User selects vehicle type "<vehicleType>"
    And User sets event date as "<eventDate>"
    When User save the event details
    When User Remove Invalid divisions if exists
    When User save the event details
    Then Verify the user can save the event details successfully
    When User click on "<Allowance type>"
    Then User selects the "<Performace type>"
    When User selects "Separate Allowances By DSD Distributor" radio button in the first stepper of the allowance workflow section
    And User clicks the checkbox preceding the text, 'I will submit Allowances for all Stores & Items, including those delivered by other Vendors and I will be funding them.'
    Then Verify "My Vendors" and "Other Vendors" headers are displayed in the dates stepper
    Then User will click on Edit Divison Dates
    When User connects to the MongoDB instance of a specific environment
    And User gets a reference to the "<collectionNameOne>" collection
    And User searches by "<attributeNameOne>" and "<ppgId>" in the referenced collection for "<valueTypeOne>"
    And User retrieves the values of the array field "simsSubAccntVendors"
    And User gets a reference to the "<collectionNameTwo>" collection
    And User searches by "<attributeNameTwo>" with each of the retrieved array field's value for "<valueTypeTwo>" and stores the results in an array
    Then Verify whether the results stored in the array are displayed as the distributor list in the dates stepper
    When User clicks on Enter amounts
    Then Verify user should not see List cost , master cost in amount screen
    Examples:
    | vehicleType   | eventDate   | Allowance type | Performace type | collectionNameOne | attributeNameOne     | collectionNameTwo | valueTypeOne | attributeNameTwo | valueTypeTwo |ppgId |
    | Weekly Insert | FUTURE~15   | Scan           | 4U Event (52)   | planproductgroups | sourceProductGroupId | simsvendors       | number       | vendorNumber     | string       |827420|
