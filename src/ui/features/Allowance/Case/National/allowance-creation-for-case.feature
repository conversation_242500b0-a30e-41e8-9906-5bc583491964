@UI-Regression
Feature: Allowance Creation For Case
  Background:
    Given "National Merchant" logs into MEUPP application
    When  User clicks on "Add event"
    And User select the event type "National"
  @UI-20596
  Scenario Outline: Verify the Allowance creation for Case with One Allowance DSD combined by passing DSD PPG
   And User populates the product group with "<PPG>"
   And User selects vehicle type "<vehicleType>"
   And User sets event date as "<eventDate>"
   And User save the event details
   And User Remove Invalid divisions if exists
   And User save the event details
   Then Verify the user can save the event details successfully
   When User click on "<Allowance type>"
   And User selects the "<Performace type>"
   And User selects "One Allowance: DSD Combined" radio button in the first stepper of the allowance workflow section
   And User clicks on Enter amounts
   And User clicks the "Update All:" fields text box in the "Allowance Amounts" main entry screen
   And User enters "<allowanceAmount>" to the 'Update All:' fields text box in the 'Allowance Amounts' main entry screen
   Then User validates the allowance amount "<allowanceAmount>" value entered to the 'Update All:' fields text box in the 'Allowance Amounts' main entry screen
   When User click on Update All divisions
   And User click on Save all changes
   And User click on Save and Create Allowance
   Then verify Allowance to be created field value in the preview header
   Examples:
   | vehicleType   | eventDate | Allowance type | Performace type      | PPG    | allowanceAmount |
   | Weekly Insert | FUTURE~15 | Case           | DSD Off Invoice (01) | 755664 | 1               |

  @UI-20596
  Scenario Outline: Verify the Allowance creation for Case with Separate Allowance by DSD distributor by passing DSD PPG
   And User populates the product group with "<PPG>"
   And User selects vehicle type "<vehicleType>"
   And User sets event date as "<eventDate>"
   And User save the event details
   And User Remove Invalid divisions if exists
   And User save the event details
   Then Verify the user can save the event details successfully
   When User click on "<Allowance type>"
   And User selects the "<Performace type>"
   And User selects "Separate Allowances By DSD Distributor" radio button in the first stepper of the allowance workflow section
   And User clicks on Enter amounts
   And User clicks the "Update All:" fields text box in the "Allowance Amounts" main entry screen
   And User enters "<allowanceAmount>" to the 'Update All:' fields text box in the 'Allowance Amounts' main entry screen
   Then User validates the allowance amount "<allowanceAmount>" value entered to the 'Update All:' fields text box in the 'Allowance Amounts' main entry screen
   When User click on Update All divisions
   And User click on Save all changes
   And User click on Save and Create Allowance
   Then verify Allowance to be created field value in the preview header
   Examples:
   | vehicleType   | eventDate | Allowance type | Performace type     | PPG    | allowanceAmount |
   | Weekly Insert | FUTURE~15 | Case           | DSD Off Invoice (01)| 755664 | 1               |

