@UI-Preferences
Feature: Test save & deletion of preference wrt planning view
  @UI-19818 @UI-19819
  Scenario Outline: Save-Delete preference wrt planning view
    Given "National Vendor" logs into MEUPP application
    And User clicks the view dropdown
    And User selects "<viewName>" from the view dropdown
    And User selects eventStatus dropdown in planning view
    And User clicks on selectAll in eventStatus dropdown
    And User selects event status as Draft in eventStatus dropdown
    And User clicks 'Sort & Filter' icon
    And User expands the 'Event Type' Filter
    And User selects "<eventType>" from Event type in Sort & Filter
    And User expands vehicleType in sort and filter
    And User selects vehicle type as "<vehicleType>" in sort and filter  
    And User clicks the 'Apply' button in the 'Sort & Filter' section
    And User clicks on menu icon of preferences
    And User clicks on Save As New Preference Button
    And User saves a new preference
    And User selects the saved preference
    And User deletes the selected preference
    And User selects eventStatus dropdown in planning view
    And User clicks on selectAll in eventStatus dropdown
    And User clicks 'Sort & Filter' icon
    And User expands the 'Event Type' Filter
    And User selects "<eventType>" from Event type in Sort & Filter
    And User expands vehicleType in sort and filter
    And User selects vehicle type as "<vehicleType>" in sort and filter  
    And User clicks the 'Apply' button in the 'Sort & Filter' section
    And User clicks on menu icon of preferences
    And User clicks on Save As New Preference Button
    And User saves a new preference
    And User selects the saved preference
    And User deletes the selected preference
    Examples:
    | viewName | eventType | vehicleType |
    | Planning | National  | Friday ROP |