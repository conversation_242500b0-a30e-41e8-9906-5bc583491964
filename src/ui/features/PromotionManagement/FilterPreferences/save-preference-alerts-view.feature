@UI-Preferences
Feature: Test Preferences in AlertView
  @UI-19813
  Scenario Outline: Test save new preference wrt Alert view
    Given "National Vendor" logs into MEUPP application
    When User clicks the view dropdown
    And User selects 'Alerts' from the view dropdown
    And User clicks on sort dropdown
    When User selects "<optionValue>" option from sort dropdown
    And User clicks on filter types dropdown in Alerts view
    And User selects 'New Item' in filter types dropdown of Alert view
    And User clicks 'Sort & Filter' icon
    And User expands the 'Event Type' Filter
    And User selects "<eventType>" from Event type in Sort & Filter
    And User expands Allowance Type in sort and filter
    And User selects Allowance type as '<allowanceType>' in sort and filter
    And User expands promo type in sort and filter
    And User selects promo type as '<promoType>' in sort and filter
    And User expands vehicleType in sort and filter
    And User selects vehicle type as "<vehicleType>" in sort and filter
    And User clicks the 'Apply' button in the 'Sort & Filter' section
    And User clicks on menu icon of preferences
    And User clicks on Save As New Preference Button
    And User saves a new preference

    Examples:
      | eventType | vehicleType | allowanceType | promoType | allowanceType | optionValue          |
      | National  | Friday ROP  | Case          | Cents Off | Case          | Vendor + Event Start |
