@UI-Preferences
Feature: Save a new preference as a National Vendor & Merchant
  @UI-19821 @UI-19823
  Scenario Outline: Save new preference using sort and filter options
    Given "National Vendor" logs into MEUPP application
    And User clicks the view dropdown
    And User selects "<viewName>" from the view dropdown
    And User clicks 'Sort & Filter' icon
    And User expands the 'Event Type' Filter
    And User selects "<eventType>" from Event type in Sort & Filter
    And User expands vehicleType in sort and filter
    And User selects vehicle type as "<vehicleType>" in sort and filter  
    And User clicks the 'Apply' button in the 'Sort & Filter' section
    And User clicks on menu icon of preferences
    And User clicks on Save As New Preference Button
    And User saves a new preference
    And User selects the saved preference
    And User clicks 'Sort & Filter' icon
    And User expands vehicleType in sort and filter
    And User selects vehicle type as "<vehicleType>" in sort and filter  
    And User clicks the 'Apply' button in the 'Sort & Filter' section
    And User clicks on menu icon of preferences
    And User clicks on Update Preference Button
    And User clicks on save button
    And User deletes the selected preference
    Examples:
    | viewName   | eventType | vehicleType |
    | Planning   | National  | Friday ROP |
    | Allowances | National  | Friday ROP |

  @UI-19805
  Scenario Outline: Save new preference using sort and filter options & test delete for merchant login
    Given "National Merchant" logs into MEUPP application
    And User clicks the view dropdown
    And User selects "<viewName>" from the view dropdown
    And User clicks 'Sort & Filter' icon
    And User expands the 'Event Type' Filter
    And User selects "<eventType>" from Event type in Sort & Filter
    And User expands vehicleType in sort and filter
    And User selects vehicle type as "<vehicleType>" in sort and filter  
    And User clicks the 'Apply' button in the 'Sort & Filter' section
    And User clicks on menu icon of preferences
    And User clicks on Save As New Preference Button
    And User saves a new preference
    And User selects the saved preference
    And User clicks 'Sort & Filter' icon
    And User expands vehicleType in sort and filter
    And User selects vehicle type as "<vehicleType>" in sort and filter  
    And User clicks the 'Apply' button in the 'Sort & Filter' section
    And User clicks on menu icon of preferences
    And User clicks on Update Preference Button
    And User clicks on save button
    And User deletes the selected preference
    Examples:
    | viewName   | eventType | vehicleType |
    | Allowances | National  | Friday ROP |