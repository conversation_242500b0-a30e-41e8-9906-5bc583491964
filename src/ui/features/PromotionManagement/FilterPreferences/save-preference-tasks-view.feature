@UI-Preferences
Feature:Manage Preferences in Tasks View with Select/Deselect All Functionality
  @UI-19830
  Scenario Outline: Select/Deselect All in Tasks View
    Given "National Merchant" logs into MEUPP application
    And User clicks the view dropdown
    And User clicks the Filter By Type dropdown in Tasks View
    And User selects all items from the Filter By Type dropdown in Tasks View
    And User clicks on Apply button
    And User selects "<viewName>" from the view dropdown
    And User clicks 'Sort & Filter' icon
    And User expands the 'Event Type' Filter
    And User selects "<eventType>" from Event type in Sort & Filter
    And User expands the 'Group and Categories' filter
    And User selects all the checkboxes under 'Group and Categories'
    And User expands vehicleType in sort and filter
    And User selects vehicle type as "<vehicleType>" in sort and filter
    And User clicks the 'Apply' button in the 'Sort & Filter' section
    And User clicks on menu icon of preferences
    And User clicks on Save As New Preference Button
    And User saves a new preference
    And User selects the saved preference
    And User deletes the selected preference
    Examples:
    | viewName | eventType | vehicleType |
    | Tasks    | National  | Friday ROP  |

  @UI-19810
  Scenario Outline: Save and Delete preference wrt Task view
    Given "National Vendor" logs into MEUPP application
    And User clicks 'Sort & Filter' icon
    And User expands the 'Event Type' Filter
    And User selects "<eventType>" from Event type in Sort & Filter
    And User clicks the 'Apply' button in the 'Sort & Filter' section
    And User clicks on menu icon of preferences
    And User clicks on Save As New Preference Button
    And User saves a new preference
    And User selects the saved preference
    And User deletes the selected preference
    Examples:
    | eventType |
    | National  |

  @UI-19809
  Scenario Outline: Save new preference with filtertypes wrt Task view
    Given "National Vendor" logs into MEUPP application
    And User clicks on filter types dropdown
    And User selects or deselects all filter types in the dropdown
    And User clicks on 'Apply' button in the filter types dropdown
    And User clicks 'Sort & Filter' icon
    And User expands the 'Event Type' Filter
    And User selects "<eventType>" from Event type in Sort & Filter
    And User expands Allowance Type in sort and filter
    And User selects Allowance type as '<allowanceType>' in sort and filter
    And User expands promo type in sort and filter
    And User selects promo type as '<promoType>' in sort and filter
    And User expands vehicleType in sort and filter
    And User selects vehicle type as "<vehicleType>" in sort and filter  
    And User clicks the 'Apply' button in the 'Sort & Filter' section
    And User clicks on menu icon of preferences
    And User clicks on Save As New Preference Button
    And User saves a new preference
    Examples:
    | eventType | vehicleType | allowanceType | promoType |
    | National  | Friday ROP  |     Case      | Cents Off |

  @UI-19808
  Scenario Outline: Save new preference with a specific filtertype wrt Task view
    Given "National Vendor" logs into MEUPP application
    And User clicks on filter types dropdown
    And User selects 'New Item' in filter types dropdown of Tasks view
    And User clicks on 'Apply' button in the filter types dropdown
    And User clicks 'Sort & Filter' icon
    And User expands the 'Event Type' Filter
    And User selects "<eventType>" from Event type in Sort & Filter
    And User expands Allowance Type in sort and filter
    And User selects Allowance type as '<allowanceType>' in sort and filter
    And User expands promo type in sort and filter
    And User selects promo type as '<promoType>' in sort and filter
    And User expands vehicleType in sort and filter
    And User selects vehicle type as "<vehicleType>" in sort and filter  
    And User clicks the 'Apply' button in the 'Sort & Filter' section
    And User clicks on menu icon of preferences
    And User clicks on Save As New Preference Button
    And User saves a new preference
    Examples:
    | eventType | vehicleType | allowanceType | promoType | filterType |
    | National  | Friday ROP  |     Case      | Cents Off | New Item |

  @UI-19812
  Scenario Outline: Save new preference with filtertypes wrt Task view
    Given "National Vendor" logs into MEUPP application
    And User clicks on menu icon of preferences
    And User clicks on Save As New Preference Button
    And User saves a new preference
    And User clicks 'Sort & Filter' icon
    And User expands the 'Event Type' Filter
    And User selects "<eventType>" from Event type in Sort & Filter
    And User expands Allowance Type in sort and filter
    And User selects Allowance type as '<allowanceType>' in sort and filter
    And User expands promo type in sort and filter
    And User selects promo type as '<promoType>' in sort and filter
    And User expands vehicleType in sort and filter
    And User selects vehicle type as "<vehicleType>" in sort and filter  
    And User clicks the 'Apply' button in the 'Sort & Filter' section
    And User clicks on menu icon of preferences
    And User clicks on Update Preference Button
    And User clicks on save button
    Examples:
    | eventType | vehicleType | allowanceType | promoType |
    | National  | Friday ROP  |     Case      | Cents Off |