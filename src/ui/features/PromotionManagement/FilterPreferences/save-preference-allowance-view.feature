@UI-Preferences
Feature: Test save & deletion of preference wrt Allowances view
  @UI-19823 @UI-19804
  Scenario Outline: Save-Delete preference wrt Allowances view
    Given "<userType>" logs into MEUPP application
    And User clicks the view dropdown
    And User selects "<viewName>" from the view dropdown
    And User clicks on Status dropdown in Allowance View
    And User clicks on selectorUnselectAll in the dropdown
    And User selects Event Status as "<eventStatus>" in Allowance View
    And User clicks on Apply button
    And User clicks on Allowance Type dropdown in Allowance View
    And User selects Allowance Type as "<allowanceType>" in Allowance View
    And User clicks on Apply button
    And User clicks on Performance Type dropdown in Allowance View
    And User selects Performace Type as "<performanceType>" in Allowance View
    And User clicks on Apply button
    And User clicks on Date Type dropdown in Allowance View
    And User selects Date Type in Allowance View
    And User clicks on Vehicle Type dropdown in Allowance View
    And User selects Vehicle Type as "<vehicleType>" in Allowance View
    And User clicks on Apply button
    And User clicks 'Sort & Filter' icon
    And User selects "<eventType>" from Event type in Sort & Filter
    And User expands the 'Group and Categories' filter
    And User selects all the checkboxes under 'Group and Categories'
    And User expands vehicleType in sort and filter
    And User selects vehicle type as "<vehicleType>" in sort and filter  
    And User clicks the 'Apply' button in the 'Sort & Filter' section
    And User clicks on menu icon of preferences
    And User clicks on Save As New Preference Button
    And User saves a new preference
    And User selects the saved preference
    And User deletes the selected preference
    Examples:
    |   userType        | viewName   | eventType | eventStatus | allowanceType | performanceType      | vehicleType |
    | National Vendor   | Allowances | National  | Draft       | Case          | Billback Liquor (85) | Disco       |
    | National Merchant | Allowances | National  | Draft       | Case          | Billback Liquor (85) | Disco       |

  @UI-19822
  Scenario Outline: Select/Deselect All in Allowance View
    Given "National Vendor" logs into MEUPP application
    And User clicks the view dropdown
    And User selects "<viewName>" from the view dropdown
    And User clicks on Status dropdown in Allowance View
    And User clicks on selectorUnselectAll in the dropdown
    And User clicks on Apply button
    And User clicks on Allowance Type dropdown in Allowance View
    And User clicks on selectorUnselectAll in the dropdown
    And User clicks on Apply button
    And User clicks on Performance Type dropdown in Allowance View
    And User clicks on selectorUnselectAll in the dropdown
    And User clicks on Apply button
    And User clicks on Date Type dropdown in Allowance View
    And User selects Date Type in Allowance View
    And User clicks on Vehicle Type dropdown in Allowance View
    And User clicks on selectorUnselectAll in the dropdown
    And User clicks on Apply button
    And User clicks 'Sort & Filter' icon
    And User expands the 'Event Type' Filter
    And User selects "<eventType>" from Event type in Sort & Filter
    And User expands the 'Group and Categories' filter
    And User selects all the checkboxes under 'Group and Categories'
    And User expands vehicleType in sort and filter
    And User selects vehicle type as "<vehicleType>" in sort and filter  
    And User clicks the 'Apply' button in the 'Sort & Filter' section
    And User clicks on menu icon of preferences
    And User clicks on Save As New Preference Button
    And User saves a new preference
    And User selects the saved preference
    And User deletes the selected preference
    Examples:
    | viewName   | eventType | vehicleType |
    | Allowances | National  | Friday ROP |

  @UI-19807
  Scenario Outline: Test update preference wrt Allowance view
    Given "National Merchant" logs into MEUPP application
    And User clicks the view dropdown
    And User selects "<viewName>" from the view dropdown
    And User clicks on Status dropdown in Allowance View
    And User clicks 'Sort & Filter' icon
    And User expands the 'Event Type' Filter
    And User selects "<eventType>" from Event type in Sort & Filter
    And User expands Allowance Type in sort and filter
    And User selects Allowance type as '<allowanceType>' in sort and filter
    And User expands promo type in sort and filter
    And User selects promo type as '<promoType>' in sort and filter
    And User expands vehicleType in sort and filter
    And User selects vehicle type as "<vehicleType>" in sort and filter  
    And User clicks the 'Apply' button in the 'Sort & Filter' section
    And User clicks on menu icon of preferences
    And User clicks on Save As New Preference Button
    And User saves a new preference
    Examples:
    | viewName   | eventType | vehicleType | allowanceType | promoType | filterType |
    | Allowances | National  | Friday ROP  |     Case      | Cents Off | New Item |