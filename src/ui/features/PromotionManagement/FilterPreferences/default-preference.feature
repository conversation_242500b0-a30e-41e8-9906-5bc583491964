@UI-Preferences
Feature: Setting saved preference as default for National vendor or merchant
  @UI-19824 @UI-19820 @UI-19816
  Scenario Outline: Set saved preference as default for National Vendor
    Given "National Vendor" logs into MEUPP application
    And User clicks the view dropdown
    And User selects "<viewName>" from the view dropdown
    And User clicks 'Sort & Filter' icon
    And User expands the 'Event Type' Filter
    And User selects "<eventType>" from Event type in Sort & Filter
    And User expands vehicleType in sort and filter
    And User selects vehicle type as "<vehicleType>" in sort and filter    
    And User clicks the 'Apply' button in the 'Sort & Filter' section
    And User clicks on menu icon of preferences
    And User clicks on Save As New Preference Button
    And User saves new preference as default
    And Deleting previous default preference
    Examples:
    | viewName   | eventType | vehicleType |
    | Allowances | National  | Friday ROP |
    | Planning   | National  | Friday ROP |
    | Alerts   | National  | Friday ROP |

  @UI-19806
  Scenario Outline: Set saved preference as default for National Merchant
    Given "National Merchant" logs into MEUPP application
    And User clicks the view dropdown
    And User selects "<viewName>" from the view dropdown
    And User clicks 'Sort & Filter' icon
    And User expands the 'Event Type' Filter
    And User selects "<eventType>" from Event type in Sort & Filter
    And User expands vehicleType in sort and filter
    And User selects vehicle type as "<vehicleType>" in sort and filter    
    And User clicks the 'Apply' button in the 'Sort & Filter' section
    And User clicks on menu icon of preferences
    And User clicks on Save As New Preference Button
    And User saves new preference as default
    And Deleting previous default preference
    Examples:
    | viewName   | eventType | vehicleType |
    | Allowances | National  | Friday ROP |