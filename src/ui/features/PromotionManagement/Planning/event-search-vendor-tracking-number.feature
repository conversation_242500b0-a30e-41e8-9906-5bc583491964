Feature: Seach by vendor tracking number
 @UI-18755
    Scenario: Verify search by vendor tracking number
    Given "National Vendor" logs into MEUPP application
    When User clicks on "Add event"
    When User select the event type "National"
    Then User populates the product group with "<PPG>"
    When User selects vehicle type "<vehicleType>"
    And User sets event date as "<eventDate>"
    When User save the event details
    When User Remove Invalid divisions if exists
    When User save the event details
    Then Verify the user can save the event details successfully
    When User click on "<Allowance type>"
    Then User selects the "<Performace type>"
    When User clicks on header flat allowance amount text box
    When User clicks on the header flat allowance amount
    And User enters the header flat allowance default amount
    When User click on continue to Billing Details
    And User enters vendor tracking number "123456"
    And User click on Save and Create Allowance
    Then verify Allowance to be created field value in the preview header
    And User clicks BACK button on the event details page
    And User clicks on search box
    When User clicks on 'View' dropdown
    And User selects 'Planning' from the view dropdown
    And User clicks the default-selected 'Offer ID#' search criteria option dropdown
    And User selects "Vendor Tracking #" from the search criteria option dropdown
    And User enters search text "123456" in search box
    And User clicks the 'Search' icon
    And User clicks on first search result link
    Examples:
        | vehicleType   | eventDate | PPG    | Allowance type | Performace type |
        | Weekly Insert | FUTURE~15 | 755664 | Header Flat    | Price / Ad / Display (88) |