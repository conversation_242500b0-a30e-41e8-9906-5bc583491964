Feature: search by PPG ID
 @UI-18754
 Scenario: Verify search by PPG ID
 Given "National Vendor" logs into MEUPP application
    When User clicks on 'View' dropdown
    And User selects 'Planning' from the view dropdown
    And User clicks the default-selected 'Offer ID#' search criteria option dropdown
    And User selects "PPG ID#" from the search criteria option dropdown
    When User enters search text "755664" in search box
    And User clicks the 'Search' icon
    And User clicks on first search result link