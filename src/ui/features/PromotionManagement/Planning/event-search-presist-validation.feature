@UI-Regression
Feature: Value in search box should presist
@UI-18756
Scenario: Verify search value persists after navigating away and returning to the page
  Given "National Vendor" logs into MEUPP application
  When User clicks on 'View' dropdown
  And User selects 'Planning' from the view dropdown
  And User clicks the default-selected 'Offer ID#' search criteria option dropdown
  And User selects "Event Name" from the search criteria option dropdown
  And User enters search text "andalou" in search box
  And User clicks the 'Search' icon
  And User clicks on first search result link
  And User clicks BACK button on the event details page
  And User clicks on search box
  Then User verifies search value "andalou" persists in search box