Feature: search by CIC ID
@UI-18752
    Scenario: Verify search by CIC ID
    Given "National Vendor" logs into MEUPP application
    When User clicks on 'View' dropdown
    And User selects 'Planning' from the view dropdown
    And User clicks the default-selected 'Offer ID#' search criteria option dropdown
    And User selects "CIC ID#" from the search criteria option dropdown
    When User enters search text "50010751" in search box
    And User clicks the 'Search' icon
    And User clicks on first search result link