@UI-Regression
Feature: Event Search Invalid Input Validation

  @UI-18757
  Scenario: Verify error message when searching with invalid input characters
    Given "National Vendor" logs into MEUPP application
    When User clicks on 'View' dropdown
    And User selects 'Planning' from the view dropdown
    And User clicks the default-selected 'Offer ID#' search criteria option dropdown
    And User selects "Event ID#" from the search criteria option dropdown
    When User enters search text "$&" in search box
    And User clicks the 'Search' icon
    Then User should see error message for invalid input