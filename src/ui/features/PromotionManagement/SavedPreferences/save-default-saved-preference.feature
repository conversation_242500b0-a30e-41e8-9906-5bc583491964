@UI-Regression
Feature: Test Preferences in AlertView
  Background: Login as Vendor
    Given "National Vendor" logs into MEUPP application
    When User clicks on 'View' dropdown
    And User selects 'Alerts' from the view dropdown
    And User clicks 'Sort & Filter' icon
    And User expands the 'Event Type' Filter
    And User selects the event type "National" in the 'Sort & Filter' section
    And User clicks the 'Apply' button in the 'Sort & Filter' section

  @UI-19813
  Scenario: Create New preference for Alert View
    And User clicks on preference dropdown
    And User selects test preference from preference dropdown
    And User opens the preference menu
    And User deletes the saved preference
    And User opens the preference menu
    And User selects save as new in preference
    And User enters preference name
    Then User saves the new preference
    
  @UI-19810
  Scenario: Delete saved preference
    And User clicks on preference dropdown
    And User selects test preference from preference dropdown
    And User opens the preference menu
    Then User selects 'delete selected' preference
      
  @UI-19812
  Scenario: Modify preferences
    And User clicks on preference dropdown
    And User selects test preference from preference dropdown
    And User opens the preference menu
    And User selects 'Update Preference'
    And User set as default preference
    Then User saves the new preference 