@UI-Regression
Feature: Search unpublished National Event as Divisional Merchant and National Merchant.
Background:Login as National Merchant
 Given "National Merchant" logs into MEUPP application
 When User clicks on "Add event"
 And User select the event type "National"
@UI-18551 @UI-18745
Scenario Outline: Verify unpublished National Event is not visible to Divisional Merchant
  And User populates the product group with "<PPG>"
  And User selects vehicle type "<vehicleType>"
  And User sets event date as "<eventDate>"
  And User save the event details
  And User Remove Invalid divisions if exists
  And User save the event details
  Then Verify the user can save the event details successfully
  And User fetches the event ID after the event is created
  And User closes the current browser context
  And User opens a new browser context
  Given "Divisional Merchant" logs into MEUPP application
  When User clicks the view dropdown
  And User selects "<viewDropdownOption>" from the view dropdown
  And User clicks on Event status dropdown in Planning view
  And User selects and Deselects All from Event Status dropdown In Planning View
  And User selects "<Event Status>" in the Event Status dropdown in Planning View
  And User clicks the default-selected 'Offer ID#' search criteria option dropdown
  And User selects "<searchCriteriaOption>" from the search criteria option dropdown
  And User enters the event ID or eventName "EventID#" in the search criteria input field
  And User clicks the 'Search' icon
  Then User verifies the display of "<errorMessage>" in the search result section
  Examples:
   | viewDropdownOption | searchCriteriaOption | Event Status | vehicleType    | eventDate  | PPG    | errorMessage                                               |
   | Planning           | Event ID#            | Draft        | Weekly Insert  | FUTURE~15  | 755664 | No records available. Please update filters and try again  |

@UI-18747
Scenario Outline: Verify unpublished National Event is visible to National Merchant
  And User populates the product group with "<PPG>"
  And User selects vehicle type "<vehicleType>"
  And User sets event date as "<eventDate>"
  And User save the event details
  And User Remove Invalid divisions if exists
  And User save the event details
  Then Verify the user can save the event details successfully
  And User fetches the event ID after the event is created
  And User closes the current browser context
  And User opens a new browser context
  Given "National Merchant" logs into MEUPP application
  When User clicks the view dropdown
  And User selects "<viewDropdownOption>" from the view dropdown
  And User clicks 'Sort & Filter' icon
  And User expands the 'Event Type' Filter
  And User selects "<eventType>" from Event type in Sort & Filter
  And User expands the 'Group and Categories' filter
  And User selects all the checkboxes under 'Group and Categories'
  And User clicks the 'Apply' button in the 'Sort & Filter' section
  And User clicks on Event status dropdown in Planning view
  And User selects and Deselects All from Event Status dropdown In Planning View
  And User selects "<Event Status>" in the Event Status dropdown in Planning View
  And User clicks the default-selected 'Offer ID#' search criteria option dropdown
  And User selects "<searchCriteriaOption>" from the search criteria option dropdown
  And User enters the event ID or eventName "EventID#" in the search criteria input field
  And User clicks the 'Search' icon
  And User clicks the National Event's record displayed as result of search in the search result section
  Examples:
   | viewDropdownOption | searchCriteriaOption | Event Status | vehicleType    | eventDate  | PPG    | eventType |
   | Planning           | Event ID#            | Draft        | Weekly Insert  | FUTURE~15  | 755664 | National  |