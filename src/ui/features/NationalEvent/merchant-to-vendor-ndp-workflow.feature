@UI-Regression
Feature: Merchant to Vendor NDP Event Workflow
  @UI-20767
  Scenario Outline: Merchant creates NDP event with allowance and V<PERSON><PERSON> approves
    Given "National Merchant" logs into MEUPP application
    When User clicks on "Add event"
    And User select the event type "National"
    And User populates the product group with "<PPG>"
    And User selects vehicle type "<vehicleType>"
    And User sets event date as "<eventDate>"
    And User save the event details
    And User Remove Invalid divisions if exists
    And User save the event details
    Then Verify the user can save the event details successfully
    When User click on "<Allowance type>"
    And User selects the "<Performace type>"
    And User clicks on header flat allowance amount text box
    And User clicks on the header flat allowance amount
    And User enters "1" to the header flat allowance amount text box in the first stepper of the allowance workflow section
    And User click on continue to Billing Details
    And User click on Save and Create Allowance
    Then verify Allowance to be created field value in the preview header
    When User fetches the event ID after the event is created
    And User hovers over 'Actions'
    And User clicks on the "<actionsOptionOne>" option under 'Actions'
    Then User verifies whether the event status is "<eventStatusOne>"
    When User closes the current browser context
    And User opens a new browser session
    And "National Vendor" logs into MEUPP application
    And User clicks on 'View' dropdown
    And User selects 'Planning' from the view dropdown
    And User clicks the default-selected 'Offer ID#' search criteria option dropdown
    And User selects "Event ID#" from the search criteria option dropdown
    And User enters the event ID or eventName "EventID#" in the search criteria input field
    And User clicks the 'Search' icon
    And User clicks the National Event's record displayed as result of search in the search result section
    And User hovers over 'Actions'
    And User clicks on the "Agree to Event" option under 'Actions'
    Then User verifies whether the event status is "Agreed"
    When User clicks on the child event
    Then Parent Event ID should be displayed at the top of the child event page for visibility
    Examples:
    | vehicleType   | eventDate | PPG    | Allowance type | Performace type            | actionsOptionOne | eventStatusOne      | eventStatusTwo |
    | Weekly Insert | FUTURE~15 | 755664 | Header Flat    | Price / Ad / Display (88)  | Send to Vendor   | Pending With Vendor | Agreed         |