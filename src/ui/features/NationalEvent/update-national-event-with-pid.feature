@UI-Regression
Feature: Auto-approval of event when PID is updated
  @UI-19838
  Scenario Outline: National Merchant updates PID event auto-approved with Send to Vendor disabled
    Given "<User Role>" logs into MEUPP application
    And User clicks the view dropdown
    And User selects "<viewDropdownOption>" from the view dropdown
    And User clicks 'Sort & Filter' icon
    And User expands the 'Division' Filter
    And User expands the 'Event Type' Filter
    And User selects "<eventType>" from Event type in Sort & Filter
    And User expands the 'Group and Categories' filter
    And User selects all the checkboxes under 'Group and Categories'
    And User clicks the 'Apply' button in the 'Sort & Filter' section
    # And User clicks the close icon of the 'Sort & Filter' section
    And User clicks on Event status dropdown in Planning view
    And User selects and Deselects All from Event Status dropdown In Planning View
    And User selects "<Event Status>" in the Event Status dropdown in Planning View
    And User Gathers EventIds List From Promotion Management Table
    And User clicks the default-selected 'Offer ID#' search criteria option dropdown
    And User selects "<searchCriteriaOption>" from the search criteria option dropdown
    And User enters the event ID or eventName "EventID#" in the search criteria input field
    And User clicks the 'Search' icon
    And User clicks the National Event's record displayed as result of search in the search result section
    And User clicks on the "<editEventDetails>" button to update the event information
    And User updates the PID to "<New PID>"
    And User clicks on the Update Event Details button to update the event information
    Then The event should be saved and updated with the new PID "<New PID>"
    And The "<Post Update Button>" button should be disabled in Actions dropdown
    Examples:
    | User Role         | viewDropdownOption | searchCriteriaOption | New PID | Update Button        | Post Update Button | editEventDetails   | Event Status | eventType |
    | National Merchant | Planning           | Event ID#            | 60740   | Update Event Details | Send to Vendor     | Edit Event Details | Agreed       | National  |

  @UI-19836
  Scenario Outline: National Vendor updates PID auto-approved with Send to Merchant enabled
    Given "<User Role>" logs into MEUPP application
    And User clicks the view dropdown
    And User selects "<viewDropdownOption>" from the view dropdown
    And User clicks 'Sort & Filter' icon
    And User expands the 'Division' Filter
    And User expands the 'Event Type' Filter
    And User selects "<eventType>" from Event type in Sort & Filter
    And User expands the 'Group and Categories' filter
    And User selects all the checkboxes under 'Group and Categories'
    And User clicks the 'Apply' button in the 'Sort & Filter' section
    # And User clicks the close icon of the 'Sort & Filter' section
    And User clicks on Event status dropdown in Planning view
    And User selects and Deselects All from Event Status dropdown In Planning View
    And User selects "<Event Status>" in the Event Status dropdown in Planning View
    And User Gathers EventIds List From Promotion Management Table
    And User clicks the default-selected 'Offer ID#' search criteria option dropdown
    And User selects "<searchCriteriaOption>" from the search criteria option dropdown
    And User enters the event ID or eventName "EventID#" in the search criteria input field
    And User clicks the 'Search' icon
    And User clicks the National Event's record displayed as result of search in the search result section
    And User clicks on the "<editEventDetails>" button to update the event information
    And User updates the PID to "<New PID>"
    And User clicks on the Update Event Details button to update the event information
    Then The event should be saved and updated with the new PID "<New PID>"
    And The "<Post Update Button>" button should be <Post Update Button State> in Actions dropdown
    Examples:
    | User Role       | Event Status | searchCriteriaOption | New PID | Update Button         | Post Update Button | Post Update Button State | viewDropdownOption | editEventDetails   | eventType |
    | National Vendor | Agreed       | Event ID#            | 758836  | Update Event Details1 | Send to Merchant   | enabled                  | Planning           | Edit Event Details | National  |

  @UI-19839
  Scenario Outline: National Vendor updates PID regardless of event Status
    Given "<User Role>" logs into MEUPP application
    And User clicks the view dropdown
    And User selects "<viewDropdownOption>" from the view dropdown
    And User clicks 'Sort & Filter' icon
    And User expands the 'Division' Filter
    And User expands the 'Event Type' Filter
    And User selects "<eventType>" from Event type in Sort & Filter
    And User expands the 'Group and Categories' filter
    And User selects all the checkboxes under 'Group and Categories'
    And User clicks the 'Apply' button in the 'Sort & Filter' section
    And User clicks the close icon of the 'Sort & Filter' section
    And User selects "<Event Status>" in the Event Status dropdown in Planning View 
    And User clicks on Event status dropdown in Planning view
    And User selects and Deselects All from Event Status dropdown In Planning View
    And User selects "<Event Status>" in the Event Status dropdown in Planning View
    And User Gathers EventIds List From Promotion Management Table
    And User clicks the default-selected 'Offer ID#' search criteria option dropdown
    And User selects "<searchCriteriaOption>" from the search criteria option dropdown
    And User enters the event ID or eventName "EventID#" in the search criteria input field
    And User clicks the 'Search' icon
    And User clicks the National Event's record displayed as result of search in the search result section
    And User clicks on the "<editEventDetails>" button to update the event information
    And User updates the PID to "<New PID>"
    And User clicks on the Update Event Details button to update the event information
    Then The event should be saved and updated with the new PID "<New PID>"
    Examples:
    | User Role       | Event Status | searchCriteriaOption | New PID | editEventDetails   | viewDropdownOption | eventType |
    | National Vendor | Agreed       | Event ID#            | 758836  | Edit Event Details | Planning           | National  |