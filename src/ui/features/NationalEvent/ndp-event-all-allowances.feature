@UI-Regression
Feature: Create NDP Event with Multiple Allowance Types
  @UI-18406
  Scenario Outline: Create NDP event with all allowance types and verify customization
    Given "National Merchant" logs into MEUPP application
    When User clicks on "Add event"
    And User select the event type "National"
    And User populates the product group with "<PPG>"
    And User selects vehicle type "<vehicleType>"
    And User sets event date as "<eventDate>"
    And User save the event details
    And User Remove Invalid divisions if exists
    And User save the event details
    Then Verify the user can save the event details successfully
    # Case Allowance
    When User click on "Case"
    And User selects "One Allowance: DSD Combined" radio button in the first stepper of the allowance workflow section
    And User clicks on Enter amounts
    And User clicks the "Update All:" fields text box in the "Allowance Amounts" main entry screen
    And User enters "<allowanceAmount>" to the 'Update All:' fields text box in the 'Allowance Amounts' main entry screen
    Then User validates the allowance amount "<allowanceAmount>" value entered to the 'Update All:' fields text box in the 'Allowance Amounts' main entry screen
    When User click on Update All divisions
    And User click on Save all changes
    And User click on Save and Create Allowance
    Then verify Allowance to be created field value in the preview header
    When User clicks the 'Add Another Offer' button that is present beneath the allowance workflow section in the event creation page
    # 'Scan' allowance creation
    And User click on "Scan"
    And User selects the "4U Event (52)"
    And User selects "One Allowance: Warehouse, DSD, or Combined" radio button in the first stepper of the allowance workflow section
    And User clicks on Enter amounts
    And User clicks the "Update All:" fields text box in the "Allowance Amounts" main entry screen
    And User enters "<allowanceAmount>" to the 'Update All:' fields text box in the 'Allowance Amounts' main entry screen
    Then User validates the allowance amount "<allowanceAmount>" value entered to the 'Update All:' fields text box in the 'Allowance Amounts' main entry screen
    When User click on Update All divisions
    And User click on Save all changes
    And User click on Save and Create Allowance
    Then verify Allowance to be created field value in the preview header
    When User clicks the 'Add Another Offer' button that is present beneath the allowance workflow section in the event creation page
    # 'Ship To Store' allowance creation
    And User click on "Ship To Store"
    And User selects "Combined DSD" radio button in the first stepper of the allowance workflow section
    And User clicks on Enter amounts
    And User clicks the "Update All:" fields text box in the "Allowance Amounts" main entry screen
    And User enters "<allowanceAmount>" to the 'Update All:' fields text box in the 'Allowance Amounts' main entry screen
    Then User validates the allowance amount "<allowanceAmount>" value entered to the 'Update All:' fields text box in the 'Allowance Amounts' main entry screen
    When User click on Update All divisions
    And User click on Save all changes
    And User click on Save and Create Allowance
    Then verify Allowance to be created field value in the preview header
    When User clicks the 'Add Another Offer' button that is present beneath the allowance workflow section in the event creation page
    # 'Header Flat' allowance creation
    And User click on "Header Flat"
    And User clicks on the header flat allowance amount
    And User enters "<allowanceAmount>" to the header flat allowance amount text box in the first stepper of the allowance workflow section
    And User click on continue to Billing Details
    And User click on Save and Create Allowance
    Then verify Allowance to be created field value in the preview header
    When User clicks the 'Add Another Offer' button that is present beneath the allowance workflow section in the event creation page
    # 'Item Flat' allowance creation
    And User click on "Item Flat"
    And User clicks on Enter amounts
    And User clicks the "Update All:" fields text box in the "Allowance Amounts" main entry screen
    And User enters "<allowanceAmount>" to the 'Update All:' fields text box in the 'Allowance Amounts' main entry screen
    Then User validates the allowance amount "<allowanceAmount>" value entered to the 'Update All:' fields text box in the 'Allowance Amounts' main entry screen
    When User click on Update All divisions
    And User click on Save all changes
    And User click on Save and Create Allowance
    Then verify Allowance to be created field value in the preview header
    Examples:
    | vehicleType   | eventDate | PPG    | allowanceAmount |
    | Weekly Insert | FUTURE~30 | 755664 | 1               |