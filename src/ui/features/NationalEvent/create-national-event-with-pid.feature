@UI-Regression
Feature: Verify National Event PID fetch verifications
  Background:
    Given "National Merchant" logs into MEUPP application
    When User clicks on "Add event"
    When User select the event type "National"

  @UPP-19840
  Scenario Outline: Verify the store groups and types based on entered PID
    When User clicks on the PID input field
    And User enters a valid PID "<PID>" in the PID input field
    When User clicks on "Fetch Event Details" button beside PID input field
    Then verify the stores attached to the PID "<ExpectedStores>" should be displayed
    Examples:
    | PID    | ExpectedStores                                   |
    | 758836 | Haggen All Stores (15),Southern All Stores (160) |

  @UPP-19841 @UPP-19837
  Scenario Outline: Verify Auto-populate and manual selection for National Event with PID
    When User clicks on the PID input field
    And User enters a valid PID "<PID>" in the PID input field
    When User clicks on "Fetch Event Details" button beside PID input field
    Then the promo product group search box should be visible
    And the promo product group search box should be enabled
    And the product group name should be visible
    And the store group type button should be visible
    And the store group type button should be enabled
    And the selection dropdown button should be visible
    And the selection dropdown button should be enabled
    And the vehicle type button should be visible
    And the vehicle type button should be enabled
    And the start week button should be visible
    And the start week button should be enabled
    And the event name input should be visible
    And the event name input should not be empty
    When User manually select store groups "<SelectableStoreGroups>"
    Then the selected store groups "<SelectableStoreGroups>" should be displayed
    Examples:
    | PID    | SelectableStoreGroups    |
    | 758836 | Seattle All Stores (220) |

  @UPP-19846
  Scenario Outline: Verify Auto-populate and manual selection for National Event with PID
    When User clicks on the PID input field
    And User enters a valid PID "<PID>" in the PID input field
    When User clicks on "Fetch Event Details" button beside PID input field
    Then the promo product group search box should be visible
    And the promo product group search box should be enabled
    And the product group name should be visible
    And the store group type button should be visible
    And the store group type button should be enabled
    And the selection dropdown button should be visible
    And the selection dropdown button should be enabled
    And the vehicle type button should be visible
    And the vehicle type button should be enabled
    And the start week button should be visible
    And the start week button should be enabled
    And the event name input should be visible
    And the event name input should not be empty
    Then No warning message should be displayed
    Examples:
    | PID    |
    | 758836 |