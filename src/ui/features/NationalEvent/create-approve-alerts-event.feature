Feature: <PERSON><PERSON><PERSON> creates event and merchant approves and checks in published events in alerts
        @UPP-19520
        Scenario Outline: <PERSON><PERSON><PERSON> creates event and merchant approves and checks in published events in alerts
                Given "National Vendor" logs into MEUPP application
                When User clicks on "Add event"
                And User select the event type "National"
                And User populates the product group with "<PPG>"
                And User selects vehicle type "<vehicleType>"
                And User sets event date as "<eventDate>"
                And User save the event details
                And User Remove Invalid divisions if exists
                And User save the event details
                Then Verify the user can save the event details successfully
                When User click on "<Allowance type>"
                And User selects the "<Performace type>"
                And User clicks on header flat allowance amount text box
                And User clicks on the header flat allowance amount
                And User enters "1" to the header flat allowance amount text box in the first stepper of the allowance workflow section
                And User click on continue to Billing Details
                And User click on Save and Create Allowance
                Then verify Allowance to be created field value in the preview header
                When User fetches the event ID after the event is created
                And User clicks on the "<actionsOptionOne>" option under 'Actions'
                Then User verifies whether the event status is "<eventStatusOne>"
                When User closes the current browser context
                And User opens a new browser context
                And "National Merchant" logs into MEUPP application
                And User clicks the view dropdown
                And User selects "<viewDropdownOption>" from the view dropdown
                And User clicks 'Sort & Filter' icon
                And User expands the 'Event Type' Filter
                And User selects all checkboxes under eventType in Sort & Filter
                And User expands the 'Group and Categories' filter
                And User selects all the checkboxes under 'Group and Categories'
                And User expands vehicleType in sort and filter
                And User selects all the checkboxes under 'Vehicle Type'
                And User clicks the 'Apply' button in the 'Sort & Filter' section
                And User clicks the default-selected 'Offer ID#' search criteria option dropdown
                And User selects "<searchCriteriaOption>" from the search criteria option dropdown
                And User enters the event ID or eventName "EventID#" in the search criteria input field
                And User clicks the 'Search' icon
                And User clicks the National Event's record displayed as result of search in the search result section
                And User clicks on the "<actionsOptionTwo>" option under 'Actions'
                When User confirms the '<actionLabel>' action on the confirmation popup
                Then User verifies the confirmation modal is visible for '<actionLabel>' action
                And User verifies the confirmation message text is visible for '<actionLabel>' action
                Then User clicks on back button to return to promotion management page
                And User clicks the view dropdown
                And User selects "<viewDropdownOptionTwo>" from the view dropdown
                And User clicks on filter types dropdown in Alerts view
                And User selects 'Event Published' in filter types dropdown of Alert view
                And User clicks the 'Search' icon
                Then User verifies the display of the event in the search result
                Examples:
                        | vehicleType   | eventDate | searchCriteriaOption | PPG    | Allowance type | Performace type           | actionsOptionOne | eventStatusOne        | actionsOptionTwo | eventStatusTwo | viewDropdownOption | viewDropdownOptionTwo | searchDefaultOption | searchDynamicDefaultOption | actionLabel         |
                        | Weekly Insert | FUTURE~0  | Event ID#            | 755664 | Header Flat    | Price / Ad / Display (88) | Send to Merchant | Pending With Merchant | Agree to Event   | Agreed         | Planning           | Alerts                | Offer ID#           | Event ID#                  | Yes, Agree to Event |