@UI-Regression
Feature: Create National Event
  @UI-20654
  Scenario: Verify the creation of a National Event by passing DSD PPG
    Given "National Merchant" logs into MEUPP application
    When User clicks on "Add event"
    When User select the event type "National"
    Then User populates the product group with "<PPG>"
    When User selects vehicle type "<vehicleType>"
    And User sets event date as "<eventDate>"
    When User save the event details
    When User Remove Invalid divisions if exists
    When User save the event details
    Then Verify the user can save the event details successfully
    Examples:
    | vehicleType   | eventDate | PPG   |
    | Weekly Insert | FUTURE~15 | 755664   |


  