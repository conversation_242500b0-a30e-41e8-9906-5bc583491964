@UI-NationalEventWorkflow
Feature: National Event Workflow - Event Status Changes
  @UI-19007
  Scenario Outline: Verify whether the National and Divisional event move to Rejected when associated offer is Rejected by Merchant
    Given "National Vendor" logs into MEUPP application
    When User clicks on "Add event"
    And User select the event type "National"
    And User populates the product group with "<PPG>"
    And User deselects "<divisionName>" division from the 'Store Groups*' dropdown in the 'Event Details' section
    And User selects vehicle type "<vehicleType>"
    And User sets event date as "<eventDate>"
    And User save the event details
    And User Remove Invalid divisions if exists
    And User save the event details
    Then Verify the user can save the event details successfully
    When User fetches the event ID after the event is created
    And User clicks on the Division count displayed within the 'Event Details' section
    And User captures the list of divisions for the national event
    And User click on "<Allowance type>"
    And User selects "One Allowance: DSD Combined" radio button in the first stepper of the allowance workflow section
    And User clicks on Enter amounts
    And User clicks the "Update All:" fields text box in the "Allowance Amounts" main entry screen
    And User enters "<allowanceAmount>" to the 'Update All:' fields text box in the 'Allowance Amounts' main entry screen
    And User validates the allowance amount "<allowanceAmount>" value entered to the 'Update All:' fields text box in the 'Allowance Amounts' main entry screen
    And User click on Update All divisions
    And User click on Save all changes
    And User click on Save and Create Allowance
    Then User will verify Division Level Breakdown is visible
    When User gets the list of divisions from the breakdown table
    Then User verifies that divisions in popper match divisions in the table
    And User verifies the event status in breakdown table is "<Status>"
    When User clicks on the "<actionsOptionOne>" option under 'Actions'
    Then User verifies whether the event status is "<eventStatusOne>"
    When User closes the current browser context
    And User opens a new browser context
    And "National Merchant" logs into MEUPP application
    And User clicks the view dropdown
    And User selects "<viewDropdownOption>" from the view dropdown
    And User clicks 'Sort & Filter' icon
    And User expands the 'Division' Filter
    And User expands the 'Event Type' Filter
    And User selects "<eventType>" from Event type in Sort & Filter
    And User expands the 'Group and Categories' filter
    And User selects all the checkboxes under 'Group and Categories'
    And User expands vehicleType in sort and filter
    And User selects vehicle type as "Friday ROP" in sort and filter  
    And User clicks the 'Apply' button in the 'Sort & Filter' section
    And User clicks the default-selected 'Offer ID#' search criteria option dropdown
    And User selects "<searchCriteriaOption>" from the search criteria option dropdown
    And User enters the event ID or eventName "EventID#" in the search criteria input field
    And User clicks the 'Search' icon
    And User clicks the National Event's record displayed as result of search in the search result section
    And User clicks on the '<actionType>' option displayed on the offer's collapsed panel
    Then User verifies the confirmation modal is visible for '<actionLabel>' action
    And User verifies the confirmation message text is visible for '<actionLabel>' action
    When User confirms the '<actionLabel>' action on the confirmation popup
    Then User verifies whether the event status is "<eventStatusThree>"
    And User verifies whether all the divisional events in the 'Event Details' tab of the 'Division Level Breakdown' table have the status as "<eventStatusThree>"
    And User verifies whether the offer's status is "<eventStatusThree>"
    And User verifies the display of the "<eventStatusThree>" for 'Canceled' or 'Rejected' offers in the 'Offers' tab
    Examples:
    | vehicleType   | eventDate | PPG    | Allowance type | Performace type      | Status | actionsOptionOne | eventStatusOne        | actionsOptionTwo | eventStatusTwo | searchCriteriaOption | eventStatusThree | viewDropdownOption | divisionName | eventType | actionType | actionLabel   | allowanceAmount   |
    | Weekly Insert | FUTURE~15 | 755664 | Case           | DSD Off Invoice (01) | Draft  | Send to Merchant | Pending With Merchant | Reject Event     | Rejected       | Event ID#            | Rejected         | Planning           | Shaws        | National  | Reject     | Reject Event  | 1                 |