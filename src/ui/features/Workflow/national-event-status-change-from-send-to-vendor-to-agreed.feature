@UI-NationalEventWorkflow
Feature: Create National Event
  @UI-20576
  Scenario: Verify the creation of a National Event using DSD PPG
    Given "National Merchant" logs into MEUPP application
    When User clicks on "Add event"
    And User select the event type "National"
    And User populates the product group with "<PPG>"
    And User selects vehicle type "<vehicleType>"
    And User sets event date as "<eventDate>"
    And User save the event details
    And User Remove Invalid divisions if exists
    And User save the event details
    Then Verify the user can save the event details successfully
    When User fetches the event ID after the event is created
    And User click on "<allowanceType>"
    And User selects "One Allowance: DSD Combined" radio button in the first stepper of the allowance workflow section
    And User clicks on Enter amounts
    And User clicks the "Update All:" fields text box in the "Allowance Amounts" main entry screen
    And User enters "<allowanceAmount>" to the 'Update All:' fields text box in the 'Allowance Amounts' main entry screen
    And User validates the allowance amount "<allowanceAmount>" value entered to the 'Update All:' fields text box in the 'Allowance Amounts' main entry screen
    And User click on Update All divisions
    And User click on Save all changes
    And User click on Save and Create Allowance
    Then verify Allowance to be created field value in the preview header
    When User clicks on the "<actionsOptionOne>" option under 'Actions'
    Then User verifies whether the event status is "<eventStatusOne>"
    When User closes the current browser context
    And User opens a new browser context
    And "National Vendor" logs into MEUPP application
    And User clicks the view dropdown
    And User selects "<viewDropdownOption>" from the view dropdown
    And User clicks 'Sort & Filter' icon
    And User expands the 'Division' Filter
    And User expands the 'Event Type' Filter
    And User selects "<eventType>" from Event type in Sort & Filter
    And User expands the 'Group and Categories' filter
    And User selects all the checkboxes under 'Group and Categories'
    And User clicks the 'Apply' button in the 'Sort & Filter' section
    And User clicks the default-selected 'Offer ID#' search criteria option dropdown
    And User selects "<searchCriteriaOption>" from the search criteria option dropdown
    And User enters the event ID or eventName "EventID#" in the search criteria input field
    And User clicks the 'Search' icon
    And User clicks the National Event's record displayed as result of search in the search result section
    And User hovers over 'Actions'
    And User clicks on the "<actionsOptionTwo>" option under 'Actions'
    Then User verifies whether the event status is "<eventStatusTwo>"
    Examples:
    | vehicleType   | eventDate | PPG    | allowanceType | actionsOptionOne | eventStatusOne      | viewDropdownOption | actionsOptionTwo | eventStatusTwo | allowanceAmount |searchCriteriaOption|eventType|
    | Weekly Insert | FUTURE~15 | 755664 | Case          | Send to Vendor   | Pending With Vendor | Planning           | Agree to Event   | Agreed         | 1               |   Event ID#        |National |

      
