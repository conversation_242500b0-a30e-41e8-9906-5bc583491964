@UI-NationalEventWorkflow
Feature: National Event Workflow - Multiple Allowances Status Changes

    @UI-19006
    Scenario Outline: Verify whether multiple allowances status changes to 'Rejected' when the National merchant Rejects an offer
        Given "National Vendor" logs into MEUPP application
        When User clicks on "Add event"
        And User select the event type "National"
        And User populates the product group with "<PPG>"
        And User deselects "<divisionName>" division from the 'Store Groups*' dropdown in the 'Event Details' section
        And User selects vehicle type "<vehicleType>"
        And User sets event date as "<eventDate>"
        And User save the event details
        And User Remove Invalid divisions if exists
        And User save the event details
        Then Verify the user can save the event details successfully
        When User fetches the event ID after the event is created
        And User clicks on the Division count displayed within the 'Event Details' section
        And User captures the list of divisions for the national event

        # Case Allowance
        When User click on "Case"
        And User selects "One Allowance: DSD Combined" radio button in the first stepper of the allowance workflow section
        And User clicks on Enter amounts
        And User clicks the "Update All:" fields text box in the "Allowance Amounts" main entry screen
        And User enters "<allowanceAmount>" to the 'Update All:' fields text box in the 'Allowance Amounts' main entry screen
        Then User validates the allowance amount "<allowanceAmount>" value entered to the 'Update All:' fields text box in the 'Allowance Amounts' main entry screen
        When User click on Update All divisions
        And User click on Save all changes
        And User click on Save and Create Allowance
        Then verify Allowance to be created field value in the preview header
        When User clicks the 'Add Another Offer' button that is present beneath the allowance workflow section in the event creation page
        # 'Scan' allowance creation
        And User click on "Scan"
        And User selects the "4U Event (52)"
        And User selects "One Allowance: Warehouse, DSD, or Combined" radio button in the first stepper of the allowance workflow section
        And User clicks on Enter amounts
        And User clicks the "Update All:" fields text box in the "Allowance Amounts" main entry screen
        And User enters "<allowanceAmount>" to the 'Update All:' fields text box in the 'Allowance Amounts' main entry screen
        Then User validates the allowance amount "<allowanceAmount>" value entered to the 'Update All:' fields text box in the 'Allowance Amounts' main entry screen
        When User click on Update All divisions
        And User click on Save all changes
        And User click on Save and Create Allowance
        Then verify Allowance to be created field value in the preview header
        Then User will verify Division Level Breakdown is visible
        When User gets the list of divisions from the breakdown table
        Then User verifies that divisions in popper match divisions in the table
        And User verifies the event status in breakdown table is "<Status>"
        And User hovers over 'Actions'
        When User clicks on the "<actionsOptionOne>" option under 'Actions'
        Then User verifies whether the event status is "<eventStatusOne>"
        When User closes the current browser context
        And User opens a new browser context
        And "National Merchant" logs into MEUPP application
        And User clicks the view dropdown
        And User selects "<viewDropdownOption>" from the view dropdown
        And User clicks 'Sort & Filter' icon
        And User expands the 'Division' Filter
        And User expands the 'Event Type' Filter
        And User selects "<eventType>" from Event type in Sort & Filter
        And User expands the 'Group and Categories' filter
        And User selects all the checkboxes under 'Group and Categories'
        And User clicks the 'Apply' button in the 'Sort & Filter' section
        And User clicks the default-selected 'Offer ID#' search criteria option dropdown
        And User selects "<searchCriteriaOption>" from the search criteria option dropdown
        And User enters the event ID or eventName "EventID#" in the search criteria input field
        And User clicks the 'Search' icon
        And User clicks the National Event's record displayed as result of search in the search result section
        And User clicks over 'Actions'
        And User clicks on the '<actionType>' option displayed on the offer's collapsed panel
        Then User verifies the confirmation modal is visible for '<actionLabel>' action
        And User verifies the confirmation message text is visible for '<actionLabel>' action
        When User confirms the '<actionLabel>' action on the confirmation popup
        Then User verifies whether the event status is "<eventStatusThree>"
        And User verifies whether all the divisional events in the 'Event Details' tab of the 'Division Level Breakdown' table have the status as "<eventStatusThree>"
        And User verifies whether the offer's status is "<offerStatus>"
        And User verifies the display of the "<offerStatus>" for 'Canceled' or 'Rejected' offers in the 'Offers' tab
        Then User hovers over 'Actions'
        And User verifies if '<actionsOptionFour>' is enabled in the 'Actions' dropdown


        Examples:
            | vehicleType   | eventDate | PPG    | Allowance type | Performace type      | Status | actionsOptionOne | eventStatusOne        | actionsOptionTwo | eventStatusTwo | searchCriteriaOption | eventStatusThree      | offerStatus | viewDropdownOption | divisionName | actionsOptionThree | eventType | actionType | allowanceAmount | eventType | actionLabel | actionsOptionFour |
            | Weekly Insert | FUTURE~15 | 755664 | Case           | DSD Off Invoice (01) | Draft  | Send to Merchant | Pending With Merchant | Agree to Event   | Agreed         | Event ID#            | Pending With Merchant | Rejected    | Planning           | Shaws        | Cancel Event       | National  | Cancel     | 1               | National  | Confirm     | Send to Vendor    |