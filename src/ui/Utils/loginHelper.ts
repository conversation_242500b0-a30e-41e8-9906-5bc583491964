// utils/loginHelper.ts
import fs from 'fs';
import path from 'path';
import { Page } from 'playwright';
import { getVerificationCode } from '../PageObjectModel/GmailVerificationCode';
import { authorize } from '../PageObjectModel/VerificationToken';
import { getConfig } from './config-utils';
import { fillInputByRole } from './form-utils';

export async function attemptLogin(env: string, page: Page, userRole: string) {

    const config = getConfig(env);
    const authFile = path.join(process.cwd(), config.authStateFile || `auth-${env}.json`);
    
    console.log(`Setting up global configuration for ${env} environment`);
    console.log(`Base URL: ${config.baseUrl}`);
    console.log(`Using auth state file: ${authFile}`);

    if (!fs.existsSync(authFile)) {
        console.log(`Creating new auth state file: ${authFile}`);
        const basicAuthState = { cookies: [], origins: [] };
        fs.writeFileSync(authFile, JSON.stringify(basicAuthState, null, 2));
    }

    console.log('Navigating to base URL...');
    await page.goto(config.baseUrl, { timeout: 60000, waitUntil: 'networkidle' });

    console.log('Attempting login...');
    const emailInput = page.locator('input[type="email"], input[name="loginfmt"], #i0116').first();
    await emailInput.waitFor({ state: 'visible', timeout: 20000 });
    if( userRole === 'National Merchant') {
        await emailInput.fill(config.auth.nationalMerchant.username);
    }
    else if( userRole === 'Divisional Merchant') {
        await emailInput.fill(config.auth.divisionalMerchant.username);
    }
    else if( userRole === 'National Vendor') {
        await emailInput.fill(config.auth.nationalVendor.username);
    }
    else if( userRole === 'Divisional Vendor') {
        await emailInput.fill(config.auth.divisionalVendor.username);
    }
    await page.waitForTimeout(1000);

    const nextButton = page.getByRole('button', { name: 'Next' });
    if (await nextButton.isVisible()) {
        await nextButton.click();
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(2000);
    }

    const passwordInput = await page.locator('input[type="password"], #i0118').first();

    if (userRole === 'National Merchant') {
        await passwordInput.waitFor({ state: 'visible', timeout: 30000 });
        await passwordInput.fill(config.auth.nationalMerchant.password);
        await page.waitForTimeout(1000);
    }
    else if (userRole === 'Divisional Merchant') {
        await passwordInput.waitFor({ state: 'visible', timeout: 30000 });
        await passwordInput.fill(config.auth.divisionalMerchant.password);
        await page.waitForTimeout(1000);
    }
    else if (userRole === 'National Vendor' || userRole === 'Divisional Vendor') {
        console.log('getting code.......');
        // Wait for the verification code input field to be visible, receive the verification code from Gmail and fill the field with the same
        const code: any = await authorize();
        const codes = await getVerificationCode(code);
        console.log('Fetched code:', codes);
        console.log('Waiting for verification code...', code);
        await page.waitForTimeout(5000);
        await fillInputByRole(page, "Enter the code you received", codes);
    }

    const signInButton = page.getByRole('button', { name: 'Sign in' });
    if (await signInButton.isVisible()) {
        await signInButton.click();
        try {
            await page.waitForLoadState('networkidle', { timeout: 60000 }).catch(() => {
                console.log('Network idle timeout, continuing anyway...');
            });
        } catch (error) {
            console.log('Sign in error, but continuing:', error);
        }
        await page.waitForTimeout(3000);
    }

    const staySignedInButton = page.getByRole('button', { name: 'Yes' });
    if (await staySignedInButton.isVisible({ timeout: 10000 }).catch(() => false)) {
        await staySignedInButton.click();
        await page.waitForLoadState('networkidle');
    }
    await page.waitForURL(url => url.toString().includes(config.baseUrl), { timeout: 60000 });
    await page.waitForLoadState('networkidle');
    console.log('Setup completed successfully');
    console.log('National vendor logged in sucessfully...');

}