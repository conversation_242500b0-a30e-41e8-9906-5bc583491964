// gmail-helper.ts
import fs from 'fs';
import path from 'path';
import { google } from 'googleapis';
import readline from 'readline';
import { promisify } from 'util';
import { authenticate } from '@google-cloud/local-auth';
const dirName = `${__dirname}/keheVendor`;
const TOKEN_PATH = path.join(dirName, 'token.json');
const CREDENTIALS_PATH = path.join(`${dirName}/`, 'credentials.json');
const SCOPES = ['https://www.googleapis.com/auth/gmail.readonly'];


///////////////////////////////
async function getNewTokenAPI(oAuth2Client: any) {
  const authUrl = oAuth2Client.generateAuthUrl({
    access_type: 'offline',
    scope: SCOPES,
  });
  console.log('Authorize this app by visiting this URL:\n', authUrl);

  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  const question = promisify(rl.question).bind(rl);
  const code = await question('Enter the code from that page here: ');
  rl.close();

  try {
    const { tokens } = await oAuth2Client.getToken(code); // ← EXCHANGE CODE FOR TOKEN
    oAuth2Client.setCredentials(tokens);
    fs.writeFileSync(TOKEN_PATH, JSON.stringify(tokens, null, 2)); // ← SAVE TOKEN
    console.log('✅ Token stored to', TOKEN_PATH);
    return oAuth2Client;
  } catch (err) {
    console.error('❌ Error retrieving access token', err);
    throw err;
  }
}


////////////////////////

async function gmailVerificationCodeAuthorize() {
  const content = await fs.promises.readFile(CREDENTIALS_PATH);
  const credentials = JSON.parse(content.toString());
  const { client_secret, client_id, redirect_uris } = credentials.web;

  const oAuth2Client = new google.auth.OAuth2(
    client_id,
    client_secret,
    redirect_uris[0]
  );

  // Check for previously stored token
  if (fs.existsSync(TOKEN_PATH)) {
    const token = await fs.promises.readFile(TOKEN_PATH, 'utf8');
    oAuth2Client.setCredentials(JSON.parse(token));
    return oAuth2Client;
  } else {
    const authUrl = oAuth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: SCOPES,
    });
    if (fs.existsSync(TOKEN_PATH)) {
    console.log('Token already exists.');
    return;
  }

 const code = await getNewToken(oAuth2Client);
    console.log('Authorize this app by visiting this URL:', authUrl);
   
    console.log('Token stored to', TOKEN_PATH);
    return oAuth2Client;
  }
}

async function getNewToken(oAuth2Client: any) {
  const authUrl = oAuth2Client.generateAuthUrl({
    access_type: 'offline',
    scope: SCOPES,
  });
  console.log('Authorize this app by visiting this URL:\n', authUrl);

  // const rl = readline.createInterface({
  //   input: process.stdin,
  //   output: process.stdout,
  // });

  // rl.question('\nEnter the code from that page here: ', (code: string) => {
  //   rl.close();
  //   oAuth2Client.getToken(code, (err: any, token: any) => {
  //     if (err) return console.error('Error retrieving access token', err);
  //     oAuth2Client.setCredentials(token);
  //     fs.writeFileSync(TOKEN_PATH, JSON.stringify(token, null, 2));
  //     console.log('✅ Token stored to', TOKEN_PATH);
  //   });
  // });
  const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

const question = promisify(rl.question).bind(rl);

const code = await question('Enter the code: ');
rl.close();

  // oAuth2Client.getToken(code, (err: any, token: any) => {
  //   if (err) return console.error('Error retrieving access token', err);
  //   oAuth2Client.setCredentials(token);
  //   fs.writeFileSync(TOKEN_PATH, JSON.stringify(token, null, 2));
  //   console.log('✅ Token stored to', TOKEN_PATH);
  // });
  return Promise.resolve(code);
}




//////////////////////////////////////////////////

async function saveCredentials(client) {
  const content = await fs.readFileSync(CREDENTIALS_PATH);
  const keys = JSON.parse(content.toString());
  const key = keys.installed || keys.web;
  const payload = JSON.stringify({
    type: 'authorized_user',
    client_id: key.client_id,
    client_secret: key.client_secret,
    refresh_token: client.credentials.refresh_token,
  });
  await fs.writeFileSync(TOKEN_PATH, payload);
}

// async function authorize() {
//   let client: any = await loadSavedCredentialsIfExist();
//   if (client) {
//     return client;
//   }
//   client = await authenticate({
//     scopes: SCOPES,
//     keyfilePath: CREDENTIALS_PATH,
//   }) as any;
//   if (client && client.credentials) {
//     await saveCredentials(client);
//   }
//   return client;
// }

async function loadSavedCredentialsIfExist() {
  try {
    const content = await fs.readFileSync(TOKEN_PATH);
    const credentials = JSON.parse(content.toString());
    return google.auth.fromJSON(credentials);
  } catch (err) {
    return null;
  }
}

async function listLabels(auth) {
  const gmail = google.gmail({version: 'v1', auth});
  const res = await gmail.users.labels.list({
    userId: 'me',
  });
  const labels = res.data.labels;
  if (!labels || labels.length === 0) {
    console.log('No labels found.');
    return;
  }
  console.log('Labels:');
  labels.forEach((label) => {
    console.log(`- ${label.name}`);
  });
}

//////////////////////////////////////////////////

export async function getGmmailVerificationCode(subjectKeyword = 'Your Verification Code') {
  
  authorize().then(listLabels).catch(console.error);
  // const auth = await gmailVerificationCodeAuthorize();
  // const gmail = google.gmail({ version: 'v1', auth });

  // const res = await gmail.users.messages.list({
  //   userId: 'me',
  //   q: `subject:${subjectKeyword}`,
  //   maxResults: 1,
  // });

  // const messageId = res.data.messages?.[0]?.id;
  // if (!messageId) throw new Error('No email found with the given subject');

  // const msg = await gmail.users.messages.get({
  //   userId: 'me',
  //   id: messageId,
  //   format: 'full',
  // });

  // const bodyData = msg.data.payload?.parts?.find(p => p.mimeType === 'text/plain')?.body?.data;
  // if (!bodyData) throw new Error('No plain text body found');

  // const body = Buffer.from(bodyData, 'base64').toString();
  // const match = body.match(/\d{6}/); // Extract 6-digit code
  // if (!match) throw new Error('Verification code not found');

  // return match[0];
}




////////////////////////////////////////////

// const SCOPES = ['https://www.googleapis.com/auth/gmail.readonly'];
// const TOKEN_PATH = path.join(__dirname, 'token.json');
// const CREDENTIALS_PATH = path.join(__dirname, 'credentials.json');


export async function getVerificationCode(auth) {
 // const auth = await authorize();
  const gmail = google.gmail({ version: 'v1', auth });
  let res;
try {
   res = await gmail.users.messages.list({
    userId: 'me',
    q: 'subject:Your Safeway, Inc. account verification code',
    maxResults: 1,
  });
  console.log(res.data);
} catch (err) {
  console.error('Gmail API error:', err);
}
  const messageId = res.data.messages?.[0]?.id;
  if (!messageId) {
    throw new Error('No messages found');
  }

  const message = await gmail.users.messages.get({
    userId: 'me',
    id: messageId,
  });

  const data = message.data.payload?.parts?.[0]?.body?.data
    ?? message.data.payload?.body?.data;

  if (!data) throw new Error('Email body not found');

  const decodedBody = Buffer.from(data, 'base64').toString('utf8');
  const code = decodedBody.match(/\b\d{4,8}\b/); // Adjust regex to your code format
  if (!code) throw new Error('Verification code not found');

  return Promise.resolve(code[0]);
}

async function authorize() {
  const credentials = JSON.parse(fs.readFileSync(CREDENTIALS_PATH, 'utf8'));
  const { client_secret, client_id, redirect_uris } = credentials.web;

  const oAuth2Client = new google.auth.OAuth2(
    client_id, client_secret, redirect_uris[0]
  );

  const token = JSON.parse(fs.readFileSync(TOKEN_PATH, 'utf8'));
  oAuth2Client.setCredentials(token);

  return oAuth2Client;
 }