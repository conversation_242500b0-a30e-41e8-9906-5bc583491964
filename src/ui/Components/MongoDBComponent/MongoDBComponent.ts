import { getConfig } from '../../Utils/config-utils';
import { MongoClient, Db, Collection } from 'mongodb';

export class MongoDBComponent {
  private client: MongoClient;
  private db: Db;
  private collectionName: any;
  private result: any;
  private arrayValues: string[] = [];
  public attributeValueList: string[] = [];

  constructor(env: string) {
    const config = getConfig(env);
    this.client = new MongoClient(config.uri);
    this.db = this.client.db(config.dbName);
  }

  async connect() {
    await this.client.connect();
  }

  async getReferenceToDBCollection(collectionName: string) {
    this.collectionName = this.db.collection(collectionName);
  }

  async searchByAttributeValue(attributeName: string, attributeValue: string, valueType: string) {
    let actualValue: any = attributeValue;
    if (valueType === 'number') {
      actualValue = Number(attributeValue);
      if (isNaN(actualValue)) {
        throw new Error('attributeValue must be a valid number');
      }
    }
    this.result = await this.collectionName.findOne({ [attributeName]: actualValue });
  }
  
  async getArrayFieldValues(arrayFieldName: string) {
    if (this.result && this.result[arrayFieldName]) {
      this.arrayValues = Array.isArray(this.result[arrayFieldName]) ? this.result[arrayFieldName] : [];
    }
  }
  
  // In the event of array values containing '-', this method splits each value by '-'
  // and searches for the first part of the split value in the specified attribute.
  // The results are stored in the 'attributeValueList' array.
  async searchByAttributeValueWithArrayValues(attributeName: string, valueType: string) {
    for (const value of this.arrayValues) {
      if (typeof value === 'string' && value.includes('-')) {
        const parts = value.split('-');
        await this.searchByAttributeValue(attributeName, parts[0], valueType);
        const attribute = this.result.attributeName;
        this.attributeValueList.push(attribute);
      }
    }
  }

  async getAttributeValueList(): Promise<string[]> {
    return this.attributeValueList;
  }

  async disconnect() {
    if (this.client) {
      await this.client.close();
    }
  }
}