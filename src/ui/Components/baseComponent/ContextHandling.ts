import { test, Page, BrowserContext } from '@playwright/test';
import { pageFixture } from '../../../../config/pageFixtures';

export class ContextHandling {
    readonly page: Page;

    constructor(page: Page) {
        this.page = page;
    }

    async handleContextSwitching(context: BrowserContext): Promise<void> {
        await this.page.waitForTimeout(2000);
        await context.close();
    }
    async openNewBrowserContext(context: BrowserContext): Promise<void> {
        const browser = context.browser();
        if (!browser) {
            throw new Error('Browser instance is null.');
        }
        const latestContext = await browser.newContext({ storageState: undefined });
        pageFixture.page = await latestContext.newPage();
    }
}