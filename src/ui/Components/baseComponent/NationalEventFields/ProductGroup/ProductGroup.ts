import { expect, Page } from "playwright/test";

export class ProductGroup {
    constructor(private page: Page) {}

    async populateProductGroupNational(ppgData) {
        console.log('Populating product group with:', ppgData);
        
        try {
            // Click the form area to focus
            const formDiv = this.page.locator('form > div');
            await formDiv.waitFor({ state: 'visible', timeout: 30000 });
            await formDiv.click();
            
            // Find and click the button to open autocomplete
            const autocompleteButton = this.page.locator('#abs-input-auto-complete');
            await autocompleteButton.waitFor({ state: 'visible', timeout: 30000 });
            await autocompleteButton.click();
            await this.page.waitForLoadState('networkidle');
            await this.page.waitForTimeout(1500); // Increased wait time for dropdown stability
            
            // Search by ID and click the option
            if (!ppgData?.id) {
                throw new Error('Product group ID is required');
            }
            
            // Extract numeric ID and search
            const numericId = ppgData.id.split(' - ')[0].trim();
            await this.page.keyboard.type(numericId);
            await this.page.waitForLoadState('networkidle');
            await this.page.waitForTimeout(1500);
            
            // Try to find and click the option using ppgData
            const searchedOption = this.page.getByText(ppgData.id, { exact: true });
            await searchedOption.waitFor({ state: 'visible', timeout: 30000 });
            await searchedOption.click();
            
            // Wait for selection to be processed
            await this.page.waitForLoadState('networkidle');
            await this.page.waitForTimeout(1500);
            
            // Ensure dropdown is closed
            await this.page.keyboard.press('Escape');
            
            // Verify selection
            if (ppgData.value) {
                const selectedValue = this.page.getByText(ppgData.value, { exact: false });
                await selectedValue.waitFor({ state: 'visible', timeout: 30000 });
                await expect(selectedValue).toBeVisible();
            }
            
            // Final wait for any post-selection processing
            await this.page.waitForLoadState('networkidle');
        } catch (error) {
            // Cleanup: ensure dropdown is closed
            await this.page.keyboard.press('Escape');
            console.error('Error in populateProductGroupNational:', error);
            throw new Error(`Failed to populate product group: ${error.message}`);
        }
    }
}
