import { Page, expect } from "@playwright/test";
import { EventLandingPage } from "../../../../../Playwright-files/Pages/LandingFlow/EventLandingPage";

type EventTypeKey = 'DP' | 'AO' | 'NDP';

interface EventTypeValue {
    label: string;
    value: string;
    select: number;
}

export class NewEventTypeSelect extends EventLandingPage {
    eventType: Record<EventTypeKey, EventTypeValue> = {
        "DP": {
            "label": "Division Promotion",
            "value": "DP",
            "select": 0,
        },
        "AO": {
            "label": "Division Allowance Only",
            "value": "AO",
            "select": 1
        },
        "NDP": {
            "label": "National",
            "value": "NDP",
            "select": 2
        }
    }
    constructor(public page: Page) {
        super(page);
    }
    async selectEventType(baseUrl: string, optionType: EventTypeKey) {
        const eventType = this.eventType[optionType];
        try {
            await this.page.goto(baseUrl);
            await this.page.waitForLoadState("networkidle");

            await this.verifyLandingPage('Promotion Management', 'Add Event');
            await this.goToAddEventPage('Add Event', '**/events', 'New Event');
            await this.page.waitForLoadState("networkidle");


            await this.page.getByRole('button', { name: 'Get Started' }).nth(eventType.select).click();
            await this.page.waitForURL(`**/events/create?eventType=${eventType.value}`);

            await this.page.waitForLoadState("networkidle");
            await expect(this.page.getByText('New Event')).toBeVisible();
            // await expect(this.page.getByText(eventType.label)).toBeVisible();
            await expect(this.page.locator('#abs-event-header-container-event-type-name')).toHaveText(eventType.label);
        } catch (error: any) {
            console.error('Error in selectEventType:', error);
            throw new Error(`Failed to select event type ${optionType}: ${error.message}`);
        }
    }
}