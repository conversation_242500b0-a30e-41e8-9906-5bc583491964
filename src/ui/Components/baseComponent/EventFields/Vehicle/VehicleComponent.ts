import { Page, expect } from "@playwright/test";

export class VehicleComponent {
    constructor(private page: Page) {}

    async selectVehicleType(option: 'Other' | 'Promo Cycle' | 'Custom Date') {
        try {
            // Wait for and verify the Vehicle Type/Custom Date label
            const vehicleTypeText = this.page.getByText('Vehicle Type/Custom Date', { exact: true });
            await vehicleTypeText.waitFor({ state: 'visible', timeout: 30000 });
            await expect(vehicleTypeText).toBeVisible();

            // Wait for network to be idle before proceeding
            await this.page.waitForLoadState("networkidle");

            // Click the Vehicle Type dropdown button
            const vehicleTypeBtn = this.page.getByRole('button', { name: 'Vehicle Type/Custom Date' });
            await vehicleTypeBtn.click();

            // Wait for network to stabilize after dropdown opens
            await this.page.waitForLoadState("networkidle");

            if (option === 'Custom Date') {
                // For Custom Date, use the specific tab selector
                const customDateTab = this.page.locator('p.timefram-dropdown__tabs', { hasText: 'Custom Date' });
                await customDateTab.waitFor({ state: 'visible', timeout: 30000 });
                await customDateTab.click();
                await this.page.waitForLoadState("networkidle");
                
                // Click the custom date section
                const customDateSection = this.page.locator('#abs-vehicle-menu-vehicle-customdate-sec8');
                await customDateSection.waitFor({ state: 'visible', timeout: 30000 });
                await customDateSection.click();
            } else {
                // For Other/Promo Cycle, use the vehicle section
                const vehicleSection = this.page.locator('#abs-vehicle-menu-vehicle-sec5');
                await vehicleSection.waitFor({ state: 'visible', timeout: 30000 });
                await vehicleSection.click();
                await this.page.waitForLoadState("networkidle");

                // Try different selectors for the option
                try {
                    const buttonOption = this.page.getByRole('button').filter({ hasText: new RegExp(`^${option}$`) });
                    if (await buttonOption.isVisible({ timeout: 5000 })) {
                        await buttonOption.click();
                    } else {
                        const textOption = this.page.getByText(option, { exact: true });
                        await textOption.waitFor({ state: 'visible', timeout: 30000 });
                        await textOption.click();
                    }
                } catch (err) {
                    console.error(`Failed to find ${option} with initial selectors, trying alternatives...`);
                    const anyOption = this.page.locator('div, p, span').filter({ hasText: new RegExp(`^${option}$`) });
                    await anyOption.waitFor({ state: 'visible', timeout: 30000 });
                    await anyOption.click();
                }
            }
            
            // Final wait for network to stabilize after selection
            await this.page.waitForLoadState("networkidle");
        } catch (error) {
            console.error(`Failed to select vehicle type ${option}:`, error);
            throw error;
        }
    }

    async selectYear(yearData: any) {
        const yearLabel = this.page.getByText(yearData?.label);
        await yearLabel.waitFor({ state: 'visible', timeout: 30000 });
        await expect(yearLabel).toBeVisible();

        await expect(
            this.page.getByRole('button', { name: yearData?.label }).locator('..')
        ).toContainText(yearData?.value);
        await this.page.waitForLoadState("networkidle");
    }

    async selectStartWeek(week: string) {
        try {
            const startWeekText = this.page.getByText('Start Week/Vehicle');
        await startWeekText.waitFor({ state: 'visible', timeout: 30000 });
        await expect(startWeekText).toBeVisible();

        const startWeekBtn = this.page.getByRole('button', { name: 'Start Week/Vehicle' });
        await startWeekBtn.click();

        await this.page.waitForLoadState("networkidle");
        const weekOption = this.page.getByText(week);
        await weekOption.waitFor({ state: 'visible', timeout: 30000 });
        await weekOption.click();
        await this.page.waitForLoadState("networkidle");
        } catch (error) {
            console.error(`Failed to select week ${week}:`, error);
            throw error;
        }
    }

    async verifyStartDate(date: string) {
        const vehicleStartText = this.page.getByText('Vehicle Start');
        await vehicleStartText.waitFor({ state: 'visible', timeout: 30000 });
        await expect(vehicleStartText).toBeVisible();

        await expect(
            this.page.locator('#abs-input-date-picker-uds-error > .relative').first()
        ).toContainText(date);
    }

    async verifyEndDate(date: string) {
        const vehicleEndText = this.page.getByText('Vehicle End');
        await vehicleEndText.waitFor({ state: 'visible', timeout: 30000 });
        await expect(vehicleEndText).toBeVisible();

        await expect(
            this.page.locator('#abs-input-date-picker-uds-error > .relative').nth(1)
        ).toContainText(date);
    }
}
