import { Page, expect } from "@playwright/test";

export class DivisionSelectionComponent {
    constructor(private page: Page) {}

    private async waitForDivisionLoad() {
        await this.page.waitForLoadState('domcontentloaded');
        await this.page.waitForLoadState('networkidle');
        await this.page.waitForTimeout(2000); // Small pause for stability
    }

    async selectDivision(divisionData: any) {
        console.log(`Verifying pre-populated division: ${divisionData?.name}`);

        await this.waitForDivisionLoad();

        // Verify division is pre-populated correctly
        const divisionText = this.page.locator('span.font-normal.truncate.text-dark-text').filter({ hasText: divisionData?.id });
        await divisionText.waitFor({ state: 'visible', timeout: 30000 });
        await expect(divisionText).toBeVisible();
        
        console.log('Division verification completed');
        await this.page.waitForLoadState('networkidle');
    }
}
