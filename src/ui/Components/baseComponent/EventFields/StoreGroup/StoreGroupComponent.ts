import { Page, expect } from "@playwright/test";

export class StoreGroupComponent {
    constructor(private page: Page) {}

    async getStoreGroupType(storeGroupTypeData: any) {
        const groupTypeText = this.page.getByText(storeGroupTypeData?.label);
        await groupTypeText.waitFor({ state: 'visible', timeout: 30000 });
        await expect(groupTypeText).toBeVisible();

        const typeContainer = this.page.getByRole('button', { name: storeGroupTypeData?.label }).locator('..');
        await expect(typeContainer).toContainText(storeGroupTypeData?.type);
    }

    async getStoreGroups(storeGroupsData: any) {
        try {
            // Wait for page to be ready
            await this.page.waitForLoadState('domcontentloaded');
            
            // Verify group label if provided
            if (storeGroupsData?.label) {
                const groupLabel = this.page.getByText(storeGroupsData.label);
                await groupLabel.waitFor({ state: 'visible', timeout: 30000 });
                await expect(groupLabel).toBeVisible();
            }

            // Click View Stores if it exists
            const viewStoresButton = this.page.getByText('View Stores');
            if (await viewStoresButton.isVisible()) {
                await viewStoresButton.click();
                await this.page.waitForLoadState("networkidle");

                // Verify store count if provided
                if (storeGroupsData?.totalStores) {
                    const storesCount = this.page.locator('div').filter({ 
                        hasText: new RegExp(`^Stores \\(${storeGroupsData.totalStores}\\)$`) 
                    });
                    await storesCount.waitFor({ state: 'visible', timeout: 30000 });
                    await expect(storesCount).toBeVisible();
                }

                // Close the stores view
                const closeButton = this.page.getByTestId('close-icon-div-id').locator('path');
                if (await closeButton.isVisible()) {
                    await closeButton.click();
                    await this.page.waitForLoadState("networkidle");
                }
            }
        } catch (error: any) {
            console.error('Error in getStoreGroups:', error);
            // If it's a critical error like page navigation, rethrow
            if (error.message.includes('navigation')) {
                throw error;
            }
            // Otherwise log the error but don't fail the test
            console.warn('Non-critical error in getStoreGroups:', error?.message);
        }
    }
}
