import { Page, expect } from "@playwright/test";

export class ProductGroupComponent {
    constructor(private page: Page) {}

    async selectProductGroup(ppgData:any) {
        await this.page.waitForLoadState('domcontentloaded');
        await this.page.waitForSelector('form > div');
        await this.page.locator('form > div').click();

        // Verify the label is visible
        await this.page.waitForSelector(`text=${ppgData?.label}`);
        await expect(this.page.getByText(ppgData?.label)).toBeVisible();

        // Click the autocomplete input with the specific parent div selector
        const autoComplete = this.page.locator('div')
            .filter({ hasText: /^Promo Product Groups\*Or Enter CIC IDs$/ })
            .locator('#abs-input-auto-complete');
        await autoComplete.waitFor({ state: 'visible', timeout: 30000 });
        await autoComplete.click();
        
        // Wait for dropdown and select product
        await this.page.waitForLoadState('networkidle');
        await this.page.waitForTimeout(1000);

        await this.page.locator('//input[@placeholder="Search Promo Product Groups"]').fill(ppgData?.id);
        const productOption = this.page.getByText(ppgData?.id);
        await productOption.waitFor({ state: 'visible', timeout: 30000 });
        await productOption.click();

        // await this.page.locator('//input[@placeholder="Search Promo Product Groups"]').click();

        await this.page.waitForLoadState("networkidle");
        await autoComplete.click();
        // await this.page.locator('#abs-input-auto-complete').click();

        // Verify store count after PPG selection
        if (ppgData?.storeCount) {

            const storeCountText = this.page.getByText(`Denver All Stores (${ppgData.storeCount})`);
            await storeCountText.waitFor({ state: 'visible', timeout: 30000 });
            await expect(storeCountText).toBeVisible();
        }
    }

    async viewPPGStores(viewPPGStoresData: any) {
        const viewItemsText = this.page.getByText(viewPPGStoresData?.label);
        await viewItemsText.waitFor({ timeout: 30000 });
        await expect(viewItemsText).toBeVisible();
        await viewItemsText.click();

        await this.page.waitForLoadState("networkidle");
        const modalText = this.page.getByTestId('modal-id').getByText(`Items (${viewPPGStoresData?.itemsCount})`);
        await modalText.waitFor({ state: 'visible', timeout: 30000 });
        await expect(modalText).toBeVisible();

        const closeButton = this.page.getByTestId('close-icon-div-id').getByRole('img');
        await closeButton.click();
        await this.page.waitForLoadState("networkidle");
    }
}
