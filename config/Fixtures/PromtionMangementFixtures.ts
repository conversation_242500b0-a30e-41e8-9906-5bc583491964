import { test as base, expect, chromium } from '@playwright/test';
import { PromotionManagementPage } from '../../src/ui/PageObjectModel/PromotionManagement/PromotionManagementPage';
import { DivisionPromotionCreationPage } from '../../src/ui/PageObjectModel/EventCreation/DivisionPromotion/DivisionPromotionCreationPage';

import { NationalEventCreationPage } from '../../src/ui/PageObjectModel/EventCreation/National/NationalEventCreationPage';
import { EventLandingPage } from '../../Playwright-files/Pages/LandingFlow/EventLandingPage';
import { EventCreationPage } from '../../Playwright-files/Pages/EventsFlow/EventCreationPage';
import { EventCreationWithPID } from '../../src/ui/PageObjectModel/EventCreation/EventCreationWithPID';
import { DivisionAllowanceOnlyCreationPage } from '../../src/ui/PageObjectModel/EventCreation/DivisionAllowanceOnly/DivisionAllowanceOnlyCreationPage';
import { OfferNationalsPage } from '../../Playwright-files/PageObjectModel/OfferCreation/OfferNationalsPage';
import { OfferPage } from '../../Playwright-files/Pages/OffersFlow/OfferPage';
import { DivisionSelectionComponent } from '../../src/ui/Components/baseComponent/EventFields/Division/DivisionSelectionComponent';
import { ProductGroupComponent } from '../../src/ui/Components/baseComponent/EventFields/ProductGroup/ProductGroupComponent';
import { StoreGroupComponent } from '../../src/ui/Components/baseComponent/EventFields/StoreGroup/StoreGroupComponent';
import { VehicleComponent } from '../../src/ui/Components/baseComponent/EventFields/Vehicle/VehicleComponent';
import { NewEventTypeSelect } from '../../src/ui/Components/baseComponent/NewEventTypeSelect/newEventTypeSelect';
import { NationalEventsOfferCreationSectionPage } from '../../src/ui/PageObjectModel/OfferCreation/NationalEventsOfferCreationSectionPage';



type PromtionMangementFixtures = {
  nationalEventCreationPage: NationalEventCreationPage,
  divisionPromotionCreationPage: DivisionPromotionCreationPage,
  promotionManagementPage: PromotionManagementPage,
  eventLandingPage: EventLandingPage,
  eventCreationPage: EventCreationPage,
  divisionAllowanceOnlyCreationPage: DivisionAllowanceOnlyCreationPage,
  eventCreationWithPID: EventCreationWithPID,
  offerNationalsPage: OfferNationalsPage,
  offerPage: OfferPage,
  nationalEventsOfferCreationSection: NationalEventsOfferCreationSectionPage
  newEventTypeSelect: NewEventTypeSelect
};

const test = base.extend<PromtionMangementFixtures>({
  nationalEventCreationPage: async ({ page }, use) => {
    await use(new NationalEventCreationPage(page));
  },
  divisionPromotionCreationPage: async ({ page }, use) => {
    const divisionComponent = new DivisionSelectionComponent(page);
    const productGroupComponent = new ProductGroupComponent(page);
    const storeGroupComponent = new StoreGroupComponent(page);
    const vehicleComponent = new VehicleComponent(page);
    await use(new DivisionPromotionCreationPage({ page, divisionComponent, productGroupComponent, storeGroupComponent, vehicleComponent }));
  },
  promotionManagementPage: async ({ page }, use) => {
    await use(new PromotionManagementPage(page));
  },
  eventLandingPage: async ({ page }, use) => {

    await use(new EventLandingPage(page));
  },
  eventCreationPage: async ({ page }, use) => {

    await use(new EventCreationPage(page));
  },
  divisionAllowanceOnlyCreationPage: async ({ page }, use) => {
    await use(new DivisionAllowanceOnlyCreationPage(page));
  },
  eventCreationWithPID: async ({ page }, use) => {
    await use(new EventCreationWithPID(page));
  },
  offerNationalsPage: async ({ page }, use) => {
    await use(new OfferNationalsPage(page));
  },
  offerPage: async ({ page }, use) => {
    await use(new OfferPage(page));
  },
  newEventTypeSelect: async ({ page }, use) => {
    await use(new NewEventTypeSelect(page));
  },
  nationalEventsOfferCreationSection: async ({ page }, use) => {
    await use(new NationalEventsOfferCreationSectionPage(page));
  }
});

export { expect, test, chromium };