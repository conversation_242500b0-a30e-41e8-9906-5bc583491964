import { FullConfig } from '@playwright/test';
import { clearAuthState } from '../src/ui/Utils/auth-utils';
import { promises as fs } from 'fs';

async function globalTeardown(playwrightConfig: FullConfig) {
    try {
        console.log('Starting global teardown...');
        
        // Clean up auth state after all tests
        await clearAuthState();
        
        // Clean up setup failure screenshot if it exists
        try {
            await fs.unlink('setup-failure.png');
        } catch (err) {
            // Ignore if file doesn't exist
        }

        console.log('Global teardown completed successfully');
    } catch (error) {
        console.error('Global teardown failed:', error);
        throw error;
    }
}

export default globalTeardown;
