const fs = require('fs');
const path = require('path');
const reporter = require('cucumber-html-reporter');

// Ensure the report folder exists
const reportDir = path.resolve(__dirname, '../cucumber-report');
if (!fs.existsSync(reportDir)) {
  fs.mkdirSync(reportDir, { recursive: true });
}

const options = {
  theme: 'bootstrap',
  jsonFile: path.join(reportDir, 'cucumber-report.json'),
  output: path.join(reportDir, 'cucumber-report.html'),
  reportSuiteAsScenarios: true,
  launchReport: false, // set to false if you don't want it to auto-open
  metadata: {
    "App Version": "1.0.0",
    "Test Environment": "STAGE",
    "Browser": "Chrome",
    "Platform": "Windows 11",
    "Executed": "Local"
  }
};

reporter.generate(options);
