import { PlaywrightTestConfig, devices } from '@playwright/test';
import { getConfig } from '../src/ui/Utils/config-utils';

const env = process.env.TEST_ENV || 'dev';
const config = getConfig(env);

const playwrightConfig: PlaywrightTestConfig = {
  testDir: '../',
  testMatch: [
    '**/tests/**/*.spec.ts',
    '**/Playwright-files/tests/**/*.spec.ts'
  ],
  timeout: 120000, // Global timeout for tests
  globalSetup: require.resolve('../config/global-setup'), // Fixed path
  globalTeardown: require.resolve('../config/global-teardown'), // Fixed path
  expect: {
    timeout: 60000 // Default timeout for expect operations
  },
  fullyParallel: false,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    actionTimeout: 30000,
    navigationTimeout: 60000,
    baseURL: config.baseUrl,
    trace: 'on-first-retry',
    browserName: 'chromium',
    headless: true,
    viewport: { width: 1920, height: 1080 },
    ignoreHTTPSErrors: true,
    storageState: config.authStateFile,
    launchOptions: {
      slowMo: 100,
    }
  },
  projects: [
    {
      name: 'chromium',
      use: {
        ...devices['Desktop Chrome'],
        contextOptions: {
          permissions: ['clipboard-read', 'clipboard-write']
        }
      },
    }
  ],
};

export default playwrightConfig;