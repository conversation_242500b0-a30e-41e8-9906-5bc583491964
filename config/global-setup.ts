import { chromium, FullConfig } from '@playwright/test';
import { LoginPage } from '../Playwright-files/Pages/LoginFlow/login-page';
import { getConfig } from '../src/ui/Utils/config-utils';
import path from 'path';
import fs from 'fs';
import { clickButtonByName, fillInputByRole } from "../src/ui/Utils/form-utils";
import { authorize } from "../src/ui/Components/VendorLoginComponent/VerificationToken";
import { getGmmailVerificationCode, getVerificationCode } from "../src/ui/Components/VendorLoginComponent/GmailVerificationCode";

async function globalSetup(playwrightConfig: FullConfig) {
    const env = process.env.TEST_ENV || "dev";
    const config = getConfig(env);
    
    console.log(`Setting up global configuration for ${env} environment`);
    console.log(`Base URL: ${config.baseUrl}`);
    
    const authFile = path.join(process.cwd(), config.authStateFile || `auth-${env}.json`);
    console.log(`Using auth state file: ${authFile}`);
    
    // Create basic auth state file if it doesn't exist
    if (!fs.existsSync(authFile)) {
        console.log(`Creating new auth state file: ${authFile}`);
        const basicAuthState = {
            cookies: [],
            origins: []
        };
        fs.writeFileSync(authFile, JSON.stringify(basicAuthState, null, 2));
    }
    
    const browser = await chromium.launch({
        headless: true,
        args: ['--disable-dev-shm-usage']
    });
    
    const context = await browser.newContext({
        viewport: { width: 1680, height: 1080 },
        ignoreHTTPSErrors: true,
        serviceWorkers: 'block'
    });
    
    const page = await context.newPage();
    
    try {
        console.log('Navigating to base URL...');
        // Retry navigation up to 3 times with a delay
        // This is to handle potential network issues or slow responses
        for (let attempt = 1; attempt <= 3; attempt++) {
          try {
            await page.goto(config.baseUrl, { timeout: 60000, waitUntil: 'networkidle' });
            break; // Success
          } catch (err) {
            if (attempt === 3) throw err;
            console.warn(`Navigation failed (attempt ${attempt}), retrying...`);
            await new Promise(res => setTimeout(res, 2000));
          }
        }
        
        // Always try to login
        console.log('Attempting login...');
        
        // Wait for and fill email
        const emailInput = await page.locator('input[type="email"], input[name="loginfmt"], #i0116').first();
        await emailInput.waitFor({ state: 'visible', timeout: 30000 });
        await emailInput.fill(config.auth.nationalMerchant.username);
        await page.waitForTimeout(1000);
        
        // Click Next
        const nextButton = page.getByRole('button', { name: 'Next' });
        if (await nextButton.isVisible()) {
            await nextButton.click();
            await page.waitForLoadState('networkidle');
            await page.waitForTimeout(2000);
        }
        
        // Wait for and fill password
        const passwordInput = await page.locator('input[type="password"], #i0118').first();
        await passwordInput.waitFor({ state: 'visible', timeout: 30000 });
        await passwordInput.fill(config.auth.nationalMerchant.password);
        await page.waitForTimeout(1000);
        
        // Click Sign in with better error handling
        const signInButton = page.getByRole('button', { name: 'Sign in' });
        if (await signInButton.isVisible()) {
            await signInButton.click();
            try {
                await page.waitForLoadState('networkidle', { timeout: 60000 }).catch(() => {
                    console.log('Network idle timeout, continuing anyway...');
                });
            } catch (error) {
                console.log('Sign in error, but continuing:', error);
            }
            await page.waitForTimeout(3000);
        }
        
        // Handle "Stay signed in?" prompt
        const staySignedInButton = page.getByRole('button', { name: 'Yes' });
        if (await staySignedInButton.isVisible({ timeout: 10000 }).catch(() => false)) {
            await staySignedInButton.click();
            await page.waitForLoadState('networkidle');
        }
        
        // Wait for final navigation
        await page.waitForURL(url => url.toString().includes(config.baseUrl), { timeout: 60000 });
        await page.waitForLoadState('networkidle');
        
        // Store the authenticated state
        console.log('Storing authentication state...');
        await context.storageState({ path: authFile });
        console.log('Setup completed successfully');
        
    } catch (error) {
        console.error('Setup failed:', error);
        await page.screenshot({ path: `login-failed-${env}.png` });
        throw error;
    } finally {
        await browser.close();
    }
}

export default globalSetup;