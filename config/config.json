{"dev": {"baseUrl": "https://memsp-dev.albertsons.com/memsp-ui-shell/meupp/", "auth": {"nationalMerchant": {"username": "<EMAIL>", "password": "Safeway2025@1234"}, "nationalVendor": {"username": "<EMAIL>", "password": ""}, "divisionalMerchant": {"username": "<EMAIL>", "password": "Safeway2025@1234"}, "divisionalVendor": {"username": "<EMAIL>", "password": ""}}, "domain": "https://memsp-dev.albertsons.com", "authStateFile": "auth-dev.json", "apimPath": "/abs/devint/meupp", "uri": "mongodb+srv://meupp-dev-appuser:<EMAIL>/meupp-dev?retryWrites=true&w=majority", "dbName": "meupp-dev"}, "qa1": {"baseUrl": "https://memsp-qa1.albertsons.com/memsp-ui-shell/meupp/", "auth": {"nationalMerchant": {"username": "<EMAIL>", "password": "Safeway2025@1234"}, "nationalVendor": {"username": "<EMAIL>", "password": ""}, "divisionalMerchant": {"username": "<EMAIL>", "password": "Safeway2025@1234"}, "divisionalVendor": {"username": "<EMAIL>", "password": ""}}, "domain": "https://memsp-qa1.albertsons.com", "authStateFile": "auth-qa1.json", "apimPath": "/abs/qa1int/meupp", "uri": "mongodb+srv://meupp-qa-appuser:<EMAIL>/meupp-qa", "dbName": "meupp-qa"}, "qa2": {"baseUrl": "https://memsp-qa2.albertsons.com/memsp-ui-shell/meupp/", "auth": {"nationalMerchant": {"username": "<EMAIL>", "password": "Safeway2025@1234"}, "nationalVendor": {"username": "<EMAIL>", "password": ""}, "divisionalMerchant": {"username": "<EMAIL>", "password": "Safeway2025@1234"}, "divisionalVendor": {"username": "<EMAIL>", "password": ""}}, "domain": "https://memsp-qa2.albertsons.com", "authStateFile": "auth-qa2.json", "apimPath": "/abs/qa2int/meupp", "uri": "mongodb+srv://meupp-qa2-appuser:<EMAIL>/meupp-qa2", "dbName": "meupp-qa2"}, "perf1": {"baseUrl": "https://memsp-perf1.albertsons.com/memsp-ui-shell/meupp/", "auth": {"nationalMerchant": {"username": "<EMAIL>", "password": "Safeway2025@1234"}, "nationalVendor": {"username": "<EMAIL>", "password": ""}, "divisionalMerchant": {"username": "<EMAIL>", "password": "Safeway2025@1234"}, "divisionalVendor": {"username": "<EMAIL>", "password": ""}}, "domain": "https://memsp-perf1.albertsons.com", "authStateFile": "auth-perf1.json", "apimPath": "/abs/perf1int/meupp", "uri": "", "dbName": ""}, "stage": {"baseUrl": "https://memsp-stage.albertsons.com/memsp-ui-shell/meupp/", "auth": {"nationalMerchant": {"username": "<EMAIL>", "password": "Safeway2025@1234"}, "nationalVendor": {"username": "<EMAIL>", "password": ""}, "divisionalMerchant": {"username": "<EMAIL>", "password": "Safeway2025@1234"}, "divisionalVendor": {"username": "<EMAIL>", "password": ""}}, "domain": "https://memsp-stage.albertsons.com", "authStateFile": "auth-stage.json", "apimPath": "/abs/stageint/meupp", "uri": "mongodb+srv://meupp-stage-appuser:<EMAIL>/meupp-stage", "dbName": "meupp-stage"}}