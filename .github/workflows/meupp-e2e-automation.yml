name: meupp-e2e-automation
on:
  workflow_dispatch:
  push:
    branches:
      - dev*
      - fb*
      - hf*
      - master
      - rel*

jobs:
  Run-automation-tests: 
    uses: albertsons/esgh-central-workflow-aut/.github/workflows/run-npm-scripts.yml@v4
    with:
      NODE_VERSION: '18'
      command1: "npm install && chmod +x ./node_modules/.bin/playwright"
      command2: "chmod +x ./node_modules/.bin/playwright && npx playwright install"
      command3: "npx playwright test"
      reports_folder: "playwright-report/"
      artifact_name: "playwright-report"


  sending-email:
    needs: Run-automation-tests
    if: always()
    uses: albertsons/esgh-central-workflow-aks/.github/workflows/Send_Email_Attachment.yml@v4
    with:
      EMAIL_TO: "<EMAIL>"
      EMAIL_ATTACHMENT: index.html
      EMAIL_FROM: <EMAIL>
      EMAIL_SUBJECT: "${{ github.repository }} : '${{ github.event.inputs.ENVIRONMENT }}' Test Reports for '${{ github.workflow }}', Run: '${{ github.run_number }}']"
      EMAIL_BODY: "Attached ${{ github.event.inputs.ENVIRONMENT }} test reports for ${{ github.workflow }} run ${{ github.run_number }} in ${{ github.repository }}."
      FILES: playwright-report
    secrets:
      PERSONAL_ACCESS_TOKEN: ${{ secrets.PERSONAL_ACCESS_TOKEN }}
      EMAIL_USERNAME: ${{ secrets.EMAIL_USERNAME }}
      EMAIL_PASSWORD: ${{ secrets.EMAIL_PASSWORD }}
